#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面宠物 简化版本 - 解决exe运行问题
去除复杂Unicode字符，确保exe正常运行
"""

import os
import sys
import time
import threading
import random
from PySide6.QtCore import Qt, QTimer, QPoint, QPropertyAnimation, QEasingCurve, QRect, Signal, QObject
from PySide6.QtGui import QMovie, QAction, QPainter, QPen, QBrush, QFont, QFontMetrics, QIcon
from PySide6.QtWidgets import QApplication, QWidget, QLabel, QMenu
import psutil
from pynput import mouse, keyboard

# 配置参数
SCALE_FACTOR = 0.5
JUMP_HEIGHT = 50
NO_ACTION_THRESHOLD = 10
TYPING_THRESHOLD = 3
AUDIO_CHECK_INTERVAL = 2


class SimpleAudioDetector:
    """音频检测器"""
    
    def __init__(self):
        self.target_processes = {
            'cloudmusic.exe',
            'netease cloudmusic.exe',
            'qqmusic.exe'
        }
        
        self.last_check_time = 0
        self.cache_duration = 1.5
        self.cached_result = False
    
    def is_music_running(self):
        """检测音乐软件是否运行"""
        current_time = time.time()
        
        if current_time - self.last_check_time < self.cache_duration:
            return self.cached_result
        
        self.last_check_time = current_time
        
        try:
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if proc_name in self.target_processes:
                        self.cached_result = True
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            self.cached_result = False
            return False
            
        except Exception as e:
            self.cached_result = False
            return False


class BubbleWidget(QWidget):
    """对话气泡组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.message = ""
        self.hide()
        
        self.font = QFont("Microsoft YaHei", max(int(12 * SCALE_FACTOR), 11))
        self.font_metrics = QFontMetrics(self.font)
    
    def show_message(self, message, duration=4000):
        """显示消息"""
        if self.message == message and self.isVisible():
            return
            
        self.message = message
        
        text_rect = self.font_metrics.boundingRect(message)
        text_width = text_rect.width()
        text_height = text_rect.height()
        
        padding_x = 30
        padding_y = 20
        bubble_width = max(text_width + padding_x, 120)
        bubble_height = max(text_height + padding_y, 40)
        
        self.resize(bubble_width, bubble_height)
        
        if self.parent():
            parent_pos = self.parent().pos()
            parent_size = self.parent().size()
            x = parent_pos.x() + (parent_size.width() - bubble_width) // 2
            y = parent_pos.y() - bubble_height - 15
            self.move(x, y)
        
        self.show()
        QTimer.singleShot(duration, self.hide)
    
    def paintEvent(self, event):
        """绘制气泡"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        bubble_rect = QRect(5, 5, self.width() - 10, self.height() - 20)
        painter.setBrush(QBrush(Qt.white))
        painter.setPen(QPen(Qt.black, 2))
        painter.drawRoundedRect(bubble_rect, 12, 12)
        
        tail_points = [
            QPoint(self.width() // 2 - 12, self.height() - 18),
            QPoint(self.width() // 2, self.height() - 5),
            QPoint(self.width() // 2 + 12, self.height() - 18)
        ]
        painter.drawPolygon(tail_points)
        
        painter.setPen(QPen(Qt.black))
        painter.setFont(self.font)
        painter.drawText(bubble_rect, Qt.AlignCenter, self.message)


class StateDetector(QObject):
    """状态检测器"""
    state_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.current_state = "no"
        self.last_activity = time.time()
        self.typing_count = 0
        self.monitoring = False
        
        self.audio_detector = SimpleAudioDetector()
        
        self.work_start_time = None
        self.work_total_time = 0
        self.last_work_reminder = time.time()
        
        self.keyboard_listener = None
        self.mouse_listener = None
        self.monitor_thread = None
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        
        try:
            self.keyboard_listener = keyboard.Listener(on_press=self._on_key_press)
            self.keyboard_listener.start()
            
            self.mouse_listener = mouse.Listener(on_move=self._on_mouse_move, on_click=self._on_mouse_click)
            self.mouse_listener.start()
            
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            print("状态监控已启动")
        except Exception as e:
            print(f"监控启动失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
        try:
            if self.keyboard_listener:
                self.keyboard_listener.stop()
            if self.mouse_listener:
                self.mouse_listener.stop()
        except:
            pass
            
        print("状态监控已停止")
    
    def _on_key_press(self, key):
        self.last_activity = time.time()
        self.typing_count += 1
    
    def _on_mouse_move(self, x, y):
        self.last_activity = time.time()
    
    def _on_mouse_click(self, x, y, button, pressed):
        if pressed:
            self.last_activity = time.time()
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                new_state = self._detect_state()
                if new_state != self.current_state:
                    if self.current_state == "work" and self.work_start_time:
                        self.work_total_time += time.time() - self.work_start_time
                        self.work_start_time = None
                    
                    if new_state == "work":
                        self.work_start_time = time.time()
                    
                    self.current_state = new_state
                    self.state_changed.emit(new_state)
                    print(f"状态变化: {new_state}")
                
                self.typing_count = 0
                time.sleep(AUDIO_CHECK_INTERVAL)
                
            except Exception as e:
                print(f"监控错误: {e}")
                time.sleep(3)
    
    def _detect_state(self):
        """检测当前状态"""
        current_time = time.time()
        idle_time = current_time - self.last_activity
        
        if self.typing_count >= TYPING_THRESHOLD:
            return "work"
        
        if self.audio_detector.is_music_running():
            return "music"
        
        if idle_time > NO_ACTION_THRESHOLD:
            return "no"
        
        return "no"
    
    def get_work_time_minutes(self):
        """获取累计工作时间"""
        total = self.work_total_time
        if self.work_start_time:
            total += time.time() - self.work_start_time
        return total / 60
    
    def should_show_work_reminder(self):
        """是否显示工作提醒"""
        current_time = time.time()
        work_minutes = self.get_work_time_minutes()
        
        if (work_minutes >= 30 and 
            current_time - self.last_work_reminder > 1800):
            self.last_work_reminder = current_time
            return True
        
        return False


class DesktopPet(QWidget):
    """桌面宠物主类 - 简化版本"""

    def __init__(self):
        super().__init__()

        # 基本属性
        self.current_gif = "no"
        self.drag_position = QPoint()

        # UI组件
        self.label = QLabel(self)
        self.movie = None
        self.bubble = BubbleWidget(self)

        # 功能状态
        self.smart_mode_enabled = True
        self.manual_override = False

        # 动画和定时器
        self.animation = None
        self.is_walking = False
        self.override_timer = QTimer(self)
        self.reminder_timer = QTimer(self)
        self.interaction_timer = QTimer(self)

        # 状态检测器
        self.detector = StateDetector()
        self.detector.state_changed.connect(self.on_state_change)

        # GIF文件路径
        self.gif_files = {
            "music": "./frames/music.gif",
            "work": "./frames/work.gif",
            "no": "./frames/no.gif"
        }

        # 简化的对话消息（去掉emoji避免编码问题）
        self.messages = {
            "work": [
                "专注工作中！", "保持专注～", "工作状态很棒！", "继续加油！"
            ],
            "music": [
                "享受音乐时光～", "音乐真好听！", "放松一下～", "跟着节拍摇摆！"
            ],
            "no": [
                "休息一下吧～", "适当休息很重要！", "静静陪着你～", "发呆也不错～"
            ],
            "work_reminder": [
                "工作30分钟了，休息一下眼睛！", "该活动活动了～",
                "喝口水放松一下！", "适当休息效率更高！"
            ],
            # 互动消息
            "pet": [
                "喵～被摸了！", "好舒服呀～", "再摸摸我！", "喜欢被抚摸～",
                "咕噜咕噜～", "好开心！", "继续摸摸！"
            ],
            "feed": [
                "谢谢小鱼干！", "好好吃！", "还要还要！", "最爱小鱼干了！",
                "吃饱了～", "美味！", "喵呜～谢谢！"
            ],
            "play": [
                "一起玩耍！", "好开心呀！", "我喜欢玩！", "陪我玩～",
                "太有趣了！", "再玩一会！", "玩得真开心！"
            ],
            "sleep": [
                "困了～", "想睡觉了", "陪我睡觉吧～", "好想休息～",
                "打个盹～", "睡眠时间到！", "晚安～"
            ]
        }

        # 互动冷却时间
        self.last_interaction_time = 0
        self.interaction_cooldown = 3

        self.init_ui()
        self.init_timers()
        self.load_gif("no")

        # 启动智能模式
        if self.smart_mode_enabled:
            self.detector.start_monitoring()

    def init_ui(self):
        """初始化UI"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)

        # 设置应用图标
        try:
            if os.path.exists("cat_icon.ico"):
                app = QApplication.instance()
                app.setWindowIcon(QIcon("cat_icon.ico"))
        except:
            pass

    def init_timers(self):
        """初始化定时器"""
        # 手动覆盖定时器
        self.override_timer.timeout.connect(self.resume_smart_mode)
        self.override_timer.setSingleShot(True)

        # 提醒定时器
        self.reminder_timer.timeout.connect(self.check_reminders)
        self.reminder_timer.start(20000)

        # 互动冷却定时器
        self.interaction_timer.timeout.connect(self.reset_interaction_cooldown)
        self.interaction_timer.setSingleShot(True)

        # 动画
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(2000)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)
        self.animation.finished.connect(lambda: setattr(self, 'is_walking', False))

    def load_gif(self, state):
        """加载GIF"""
        if state not in self.gif_files or state == self.current_gif:
            return

        gif_path = self.gif_files[state]
        if not os.path.exists(gif_path):
            print(f"GIF文件不存在: {gif_path}")
            return

        # 清理旧的movie
        if self.movie:
            self.movie.stop()
            self.movie.deleteLater()
            self.movie = None

        # 创建新的movie
        self.movie = QMovie(gif_path)
        if not self.movie.isValid():
            return

        # 设置到label
        self.label.setMovie(self.movie)
        self.movie.frameChanged.connect(self.resize_to_gif)
        self.movie.start()
        self.current_gif = state

        print(f"GIF加载: {state}")

    def resize_to_gif(self):
        """调整窗口大小"""
        if self.movie and self.movie.currentPixmap():
            size = self.movie.currentPixmap().size()
            scaled_width = int(size.width() * SCALE_FACTOR)
            scaled_height = int(size.height() * SCALE_FACTOR)

            self.label.resize(scaled_width, scaled_height)
            self.label.setScaledContents(True)
            self.resize(scaled_width, scaled_height)

    def on_state_change(self, new_state):
        """状态变化处理"""
        if not self.smart_mode_enabled or self.manual_override:
            return

        if new_state != self.current_gif:
            self.load_gif(new_state)

            # 显示状态消息
            if new_state in self.messages:
                message = random.choice(self.messages[new_state])
                self.bubble.show_message(message)

            print(f"智能切换: {self.current_gif} -> {new_state}")

    def check_reminders(self):
        """检查提醒"""
        if self.detector.should_show_work_reminder():
            message = random.choice(self.messages["work_reminder"])
            self.bubble.show_message(message, 6000)

            work_minutes = int(self.detector.get_work_time_minutes())
            print(f"工作提醒: 已工作{work_minutes}分钟")

    def can_interact(self):
        """检查是否可以互动"""
        current_time = time.time()
        return current_time - self.last_interaction_time > self.interaction_cooldown

    def reset_interaction_cooldown(self):
        """重置互动冷却"""
        pass

    def interact(self, interaction_type):
        """执行互动"""
        if not self.can_interact():
            return

        self.last_interaction_time = time.time()

        if interaction_type in self.messages:
            message = random.choice(self.messages[interaction_type])
            self.bubble.show_message(message)
            print(f"互动: {interaction_type} - {message}")

        # 启动冷却定时器
        self.interaction_timer.start(self.interaction_cooldown * 1000)

    def switch_gif(self):
        """手动切换GIF"""
        states = ["music", "work", "no"]
        current_index = states.index(self.current_gif) if self.current_gif in states else 0
        next_state = states[(current_index + 1) % len(states)]

        self.load_gif(next_state)

        # 启动手动覆盖
        if self.smart_mode_enabled:
            self.manual_override = True
            self.override_timer.start(300000)  # 5分钟
            print("手动切换，智能模式暂停5分钟")

    def toggle_smart_mode(self):
        """切换智能模式"""
        self.smart_mode_enabled = not self.smart_mode_enabled
        if self.smart_mode_enabled:
            self.detector.start_monitoring()
        else:
            self.detector.stop_monitoring()
        print(f"智能模式: {'启用' if self.smart_mode_enabled else '禁用'}")

    def resume_smart_mode(self):
        """恢复智能模式"""
        self.manual_override = False
        print("智能模式已恢复")

    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
            # 左键单击 = 抚摸
            self.interact("pet")
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPosition().toPoint())
        elif event.button() == Qt.MiddleButton:
            # 中键 = 喂食
            self.interact("feed")

    def mouseMoveEvent(self, event):
        """鼠标移动"""
        if event.buttons() == Qt.LeftButton and not self.drag_position.isNull():
            self.move(event.globalPosition().toPoint() - self.drag_position)

    def mouseDoubleClickEvent(self, event):
        """双击跳跃"""
        if event.button() == Qt.LeftButton:
            self.jump()
            # 双击 = 玩耍
            self.interact("play")

    def wheelEvent(self, event):
        """鼠标滚轮事件"""
        # 滚轮 = 睡觉
        self.interact("sleep")
        event.accept()

    def jump(self):
        """跳跃动画"""
        if self.is_walking:
            return

        current_pos = self.pos()
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(current_pos.x(), current_pos.y() - JUMP_HEIGHT, self.width(), self.height()))
        self.animation.setDuration(300)
        self.is_walking = True
        self.animation.finished.connect(self.jump_down)
        self.animation.start()

    def jump_down(self):
        """跳跃下落"""
        self.animation.finished.disconnect()
        current_pos = self.pos()
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(current_pos.x(), current_pos.y() + JUMP_HEIGHT, self.width(), self.height()))
        self.animation.setDuration(300)
        self.animation.finished.connect(lambda: setattr(self, 'is_walking', False))
        self.animation.start()

    def show_context_menu(self, position):
        """右键菜单"""
        menu = QMenu(self)

        # 状态信息
        work_minutes = int(self.detector.get_work_time_minutes())
        music_running = self.detector.audio_detector.cached_result
        status_action = QAction(f"状态: {self.current_gif} | 工作: {work_minutes}分钟 | 音乐: {'运行' if music_running else '未运行'}", self)
        status_action.setEnabled(False)
        menu.addAction(status_action)

        menu.addSeparator()

        # 互动选项
        interaction_menu = QMenu("互动选项", self)

        pet_action = QAction("抚摸 (左键)", self)
        pet_action.triggered.connect(lambda: self.interact("pet"))
        interaction_menu.addAction(pet_action)

        feed_action = QAction("喂食 (中键)", self)
        feed_action.triggered.connect(lambda: self.interact("feed"))
        interaction_menu.addAction(feed_action)

        play_action = QAction("玩耍 (双击)", self)
        play_action.triggered.connect(lambda: self.interact("play"))
        interaction_menu.addAction(play_action)

        sleep_action = QAction("睡觉 (滚轮)", self)
        sleep_action.triggered.connect(lambda: self.interact("sleep"))
        interaction_menu.addAction(sleep_action)

        menu.addMenu(interaction_menu)

        menu.addSeparator()

        # 切换动画
        switch_action = QAction(f"切换动画", self)
        switch_action.triggered.connect(self.switch_gif)
        menu.addAction(switch_action)

        # 智能模式
        smart_text = "禁用智能模式" if self.smart_mode_enabled else "启用智能模式"
        smart_action = QAction(f"{smart_text}", self)
        smart_action.triggered.connect(self.toggle_smart_mode)
        menu.addAction(smart_action)

        menu.addSeparator()

        # 说话功能
        talk_action = QAction("说句话", self)
        talk_action.triggered.connect(self.say_something)
        menu.addAction(talk_action)

        menu.addSeparator()

        # 隐藏和退出
        hide_action = QAction("隐藏", self)
        hide_action.triggered.connect(self.hide)
        menu.addAction(hide_action)

        quit_action = QAction("退出", self)
        quit_action.triggered.connect(QApplication.quit)
        menu.addAction(quit_action)

        menu.exec(position)

    def say_something(self):
        """主动说话"""
        if self.current_gif in self.messages:
            message = random.choice(self.messages[self.current_gif])
            self.bubble.show_message(message)

    def closeEvent(self, event):
        """关闭事件"""
        self.detector.stop_monitoring()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        # 检查GIF文件
        required_files = ["./frames/music.gif", "./frames/work.gif", "./frames/no.gif"]
        missing_files = [f for f in required_files if not os.path.exists(f)]

        if missing_files:
            print(f"缺少GIF文件: {missing_files}")
            return

        # 设置应用图标
        if os.path.exists("cat_icon.ico"):
            app.setWindowIcon(QIcon("cat_icon.ico"))

        # 创建桌面宠物
        pet = DesktopPet()
        pet.show()

        # 设置初始位置
        screen = app.primaryScreen().geometry()
        pet.move(screen.width() // 2, screen.height() // 2)

        print("桌面宠物简化版启动成功！")
        print("互动方式:")
        print("  左键单击 - 抚摸")
        print("  中键点击 - 喂食")
        print("  双击 - 玩耍")
        print("  滚轮 - 睡觉")
        print("  右键 - 菜单")
        print("  拖拽 - 移动")
        print("音频检测: 网易云音乐 + QQ音乐")
        print("智能对话: 根据状态和互动显示消息")

        sys.exit(app.exec())

    except Exception as e:
        print(f"启动失败: {e}")


if __name__ == '__main__':
    main()
