#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试V4.0音频检测功能
"""

import time
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from desktop_pet_v4 import AudioDetector

def test_audio_detection():
    """测试V4.0音频检测"""
    print("🎵 桌面宠物V4.0 音频检测测试")
    print("=" * 60)
    print("V4.0 新特性:")
    print("✅ 置信度算法 - 减少误判")
    print("✅ 历史记录平滑 - 避免波动")
    print("✅ 分级阈值 - 不同软件不同标准")
    print("✅ 快速响应 - 0.5秒检测间隔")
    print()
    
    # 创建音频检测器
    detector = AudioDetector()
    
    print("🎵 请播放/暂停音乐测试检测效果...")
    print("📊 将显示置信度变化和检测结果")
    print("⏳ 测试20秒钟...\n")
    
    last_state = None
    
    for i in range(40):  # 20秒，每0.5秒检测一次
        is_playing = detector.detect_audio()
        confidence = detector.audio_confidence
        
        # 状态变化时显示详细信息
        if is_playing != last_state:
            status = "🎵 播放中" if is_playing else "🔇 已暂停"
            print(f"\n>>> 状态变化: {status} (置信度: {confidence:.3f})")
            last_state = is_playing
        
        # 显示实时状态
        status_icon = "🎵" if is_playing else "🔇"
        confidence_bar = "█" * int(confidence * 20)
        print(f"第{i+1:2d}次 {status_icon} 置信度: {confidence:.3f} |{confidence_bar:<20}|", end="\r")
        
        time.sleep(0.5)
    
    print("\n\n🎯 测试完成！")
    print("\n📊 检测统计:")
    print(f"最终置信度: {detector.audio_confidence:.3f}")
    print(f"历史记录数: {len(detector.cpu_history)}")
    
    print("\n🎵 支持的音频软件:")
    for proc_name, config in detector.audio_processes.items():
        threshold = config['threshold']
        weight = config['weight']
        print(f"  {proc_name}: 阈值{threshold}%, 权重{weight}")
    
    print("\n✨ V4.0 优势:")
    print("1. 🎯 置信度算法 - 平滑过渡，减少抖动")
    print("2. 📈 历史记录 - 3次平均值，避免瞬时波动")
    print("3. ⚖️ 分级阈值 - 音乐软件0.05%，浏览器1.0%")
    print("4. ⚡ 快速响应 - 0.5秒检测，暂停立即识别")

def test_confidence_algorithm():
    """测试置信度算法"""
    print("\n" + "=" * 60)
    print("🧮 置信度算法测试")
    print("=" * 60)
    
    detector = AudioDetector()
    
    # 模拟不同的音频活动场景
    scenarios = [
        {"name": "Spotify播放", "processes": [("spotify.exe", 2.0)]},
        {"name": "浏览器音频", "processes": [("chrome.exe", 3.0)]},
        {"name": "多个音频源", "processes": [("spotify.exe", 1.5), ("chrome.exe", 2.0)]},
        {"name": "低活动", "processes": [("spotify.exe", 0.02)]},
        {"name": "无音频", "processes": []},
    ]
    
    for scenario in scenarios:
        print(f"\n📋 场景: {scenario['name']}")
        
        # 模拟CPU数据
        for proc_name, cpu in scenario['processes']:
            if proc_name not in detector.cpu_history:
                detector.cpu_history[proc_name] = detector.cpu_history.__class__(maxlen=3)
            detector.cpu_history[proc_name].append(cpu)
        
        # 计算置信度
        current_score = 0.0
        for proc_name, cpu in scenario['processes']:
            if proc_name in detector.audio_processes:
                config = detector.audio_processes[proc_name]
                if cpu > config['threshold']:
                    score = min(cpu / (config['threshold'] * 5), 1.0) * config['weight']
                    current_score += score
                    print(f"  {proc_name}: CPU {cpu}% -> 得分 {score:.3f}")
        
        target_confidence = min(current_score, 1.0)
        print(f"  总得分: {current_score:.3f}")
        print(f"  目标置信度: {target_confidence:.3f}")
        
        # 模拟平滑过渡
        old_confidence = detector.audio_confidence
        new_confidence = (old_confidence * 0.7) + (target_confidence * 0.3)
        detector.audio_confidence = new_confidence
        
        is_playing = new_confidence > detector.confidence_threshold
        print(f"  平滑后置信度: {new_confidence:.3f}")
        print(f"  检测结果: {'🎵 播放' if is_playing else '🔇 暂停'}")

if __name__ == "__main__":
    try:
        test_audio_detection()
        test_confidence_algorithm()
    except KeyboardInterrupt:
        print("\n\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n❌ 测试出错: {e}")
        import traceback
        traceback.print_exc()
