#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户状态监控模块
监控用户活动并识别当前状态
"""

import time
import threading
from typing import Callable, Optional
from enum import Enum

try:
    import psutil
    import pynput
    from pynput import mouse, keyboard

    # Windows音频检测
    try:
        import pycaw
        from pycaw.pycaw import AudioUtilities, AudioEndpointVolume
        AUDIO_DETECTION_AVAILABLE = True
    except ImportError:
        AUDIO_DETECTION_AVAILABLE = False

    MONITORING_AVAILABLE = True
except ImportError:
    MONITORING_AVAILABLE = False
    AUDIO_DETECTION_AVAILABLE = False
    print("⚠️ 状态监控功能需要安装额外依赖: pip install psutil pynput pycaw")


class UserState(Enum):
    """用户状态枚举"""
    MUSIC = "music"         # 听音乐
    WORK = "work"           # 打字工作
    NO = "no"               # 无动作/空闲


class StatusMonitor:
    """用户状态监控器"""
    
    def __init__(self, callback: Optional[Callable[[UserState], None]] = None):
        self.callback = callback
        self.current_state = UserState.NO
        self.last_activity_time = time.time()
        self.typing_count = 0
        self.mouse_count = 0
        self.monitoring = False
        
        # 监控线程
        self.monitor_thread = None
        self.keyboard_listener = None
        self.mouse_listener = None
        
        # 状态检测参数
        self.no_action_threshold = 10  # 10秒无活动视为无动作
        self.typing_threshold = 3       # 3秒内多次按键视为打字工作

        # 性能优化参数
        self.audio_check_interval = 3   # 音频检测间隔（秒）
        self.last_audio_check = 0
        self.cached_audio_state = False

        # 音频检测相关
        self.audio_sessions = []
        self.audio_endpoint = None
        self._init_audio_detection()

    def _init_audio_detection(self):
        """初始化音频检测"""
        if not AUDIO_DETECTION_AVAILABLE:
            return

        try:
            from pycaw.pycaw import AudioUtilities
            # 获取默认音频设备
            devices = AudioUtilities.GetSpeakers()
            if devices:
                self.audio_endpoint = devices
                print("✅ 音频检测初始化成功")
        except Exception as e:
            print(f"⚠️ 音频检测初始化失败: {e}")
            self.audio_endpoint = None

    def start_monitoring(self):
        """开始监控用户状态"""
        if not MONITORING_AVAILABLE:
            print("❌ 状态监控功能不可用，请安装依赖包")
            return False
            
        if self.monitoring:
            return True
            
        try:
            self.monitoring = True
            
            # 启动键盘监听
            self.keyboard_listener = keyboard.Listener(
                on_press=self._on_key_press,
                on_release=self._on_key_release
            )
            self.keyboard_listener.start()
            
            # 启动鼠标监听
            self.mouse_listener = mouse.Listener(
                on_move=self._on_mouse_move,
                on_click=self._on_mouse_click
            )
            self.mouse_listener.start()
            
            # 启动状态检测线程
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            print("✅ 用户状态监控已启动")
            return True
            
        except Exception as e:
            print(f"❌ 启动状态监控失败: {e}")
            self.monitoring = False
            return False

    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
        if self.keyboard_listener:
            self.keyboard_listener.stop()
        if self.mouse_listener:
            self.mouse_listener.stop()
            
        print("⏹️ 用户状态监控已停止")

    def _on_key_press(self, key):
        """键盘按下事件"""
        self.last_activity_time = time.time()
        self.typing_count += 1

    def _on_key_release(self, key):
        """键盘释放事件"""
        pass

    def _on_mouse_move(self, x, y):
        """鼠标移动事件"""
        self.last_activity_time = time.time()

    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击事件"""
        if pressed:
            self.last_activity_time = time.time()
            self.mouse_count += 1

    def _monitor_loop(self):
        """监控主循环 - 性能优化版本"""
        check_interval = 2.0  # 基础检测间隔
        last_state_change = time.time()

        while self.monitoring:
            try:
                new_state = self._detect_current_state()

                if new_state != self.current_state:
                    self.current_state = new_state
                    last_state_change = time.time()
                    print(f"🔄 状态变化: {new_state.value}")

                    if self.callback:
                        self.callback(new_state)

                # 重置计数器
                self.typing_count = 0
                self.mouse_count = 0

                # 动态调整检测间隔：状态稳定时降低检测频率
                time_since_change = time.time() - last_state_change
                if time_since_change > 30:  # 30秒无状态变化
                    sleep_time = min(5.0, check_interval * 2)  # 最多5秒间隔
                else:
                    sleep_time = check_interval

                time.sleep(sleep_time)

            except Exception as e:
                print(f"❌ 状态检测错误: {e}")
                time.sleep(5)

    def _detect_current_state(self) -> UserState:
        """检测当前用户状态 - 按优先级权重：work > music > no"""
        current_time = time.time()

        # 优先级1: 检查是否在打字工作（最高优先级）
        if self.typing_count > 3:  # 2秒内超过3次按键视为打字工作
            return UserState.WORK

        # 优先级2: 检查是否在听音乐（中等优先级）
        try:
            # 直接检查系统音频，不再依赖特定应用程序
            if self._is_audio_playing():
                return UserState.MUSIC

        except Exception as e:
            print(f"⚠️ 音频检测错误: {e}")

        # 优先级3: 检查是否无动作（最低优先级，默认状态）
        idle_time = current_time - self.last_activity_time
        if idle_time > self.no_action_threshold:
            return UserState.NO

        # 如果有活动但不是打字或音乐，也返回无动作状态
        return UserState.NO

    def _is_audio_playing(self) -> bool:
        """检查系统是否有音频播放"""
        current_time = time.time()

        # 使用缓存减少检测频率，提升性能
        if current_time - self.last_audio_check < self.audio_check_interval:
            return self.cached_audio_state

        self.last_audio_check = current_time

        if not AUDIO_DETECTION_AVAILABLE or not self.audio_endpoint:
            # 降级到进程检测
            self.cached_audio_state = self._fallback_audio_detection()
            return self.cached_audio_state

        try:
            from pycaw.pycaw import AudioUtilities

            # 检查所有音频会话
            sessions = AudioUtilities.GetAllSessions()
            for session in sessions:
                if session.Process and session.Process.name():
                    # 检查音频会话是否活跃
                    volume = session.SimpleAudioVolume
                    if volume and not volume.GetMute() and volume.GetMasterVolume() > 0:
                        # 检查进程是否在播放音频
                        try:
                            # 简单的音频活动检测
                            if hasattr(session, 'State') and session.State == 1:  # AudioSessionStateActive
                                self.cached_audio_state = True
                                return True
                        except:
                            continue

            self.cached_audio_state = False
            return False

        except Exception as e:
            # 出错时降级到进程检测
            self.cached_audio_state = self._fallback_audio_detection()
            return self.cached_audio_state

    def _fallback_audio_detection(self) -> bool:
        """降级音频检测方法"""
        try:
            # 检查常见音频进程
            audio_processes = {
                'spotify.exe', 'netease cloudmusic.exe', 'qqmusic.exe',
                'vlc.exe', 'potplayer.exe', 'foobar2000.exe'
            }

            for proc in psutil.process_iter(['name', 'cpu_percent']):
                if proc.info['name'].lower() in audio_processes:
                    if proc.info['cpu_percent'] > 0.5:  # CPU使用率大于0.5%
                        return True
            return False
        except:
            return False

    def get_current_state(self) -> UserState:
        """获取当前状态"""
        return self.current_state

    def is_monitoring(self) -> bool:
        """检查是否正在监控"""
        return self.monitoring


# 状态到动画的映射
STATE_TO_ANIMATION = {
    UserState.MUSIC: "music",
    UserState.WORK: "work",
    UserState.NO: "no"
}
