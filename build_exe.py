#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包桌面宠物为exe文件
"""

import os
import sys
import subprocess
import shutil

def check_requirements():
    """检查打包要求"""
    print("🔍 检查打包要求...")
    
    # 检查必要文件
    required_files = [
        "desktop_pet_v6.py",
        "./frames/music.gif",
        "./frames/work.gif", 
        "./frames/no.gif"
    ]
    
    missing_files = []
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"❌ 缺少必要文件: {missing_files}")
        return False
    
    # 检查PyInstaller
    try:
        import PyInstaller
        print(f"✅ PyInstaller版本: {PyInstaller.__version__}")
    except ImportError:
        print("❌ 未安装PyInstaller，正在安装...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pyinstaller"])
            print("✅ PyInstaller安装成功")
        except subprocess.CalledProcessError:
            print("❌ PyInstaller安装失败")
            return False
    
    return True

def create_icon():
    """创建图标"""
    print("🎨 创建猫咪图标...")
    
    if os.path.exists("cat_icon.ico"):
        print("✅ 图标文件已存在")
        return True
    
    try:
        # 尝试创建图标
        exec(open("create_cat_icon.py").read())
        return os.path.exists("cat_icon.ico")
    except Exception as e:
        print(f"⚠️ 图标创建失败: {e}")
        print("将使用默认图标")
        return False

def build_exe():
    """构建exe文件"""
    print("🔨 开始构建exe文件...")
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 无控制台窗口
        "--name=桌面宠物",               # 程序名称
        "--add-data=frames;frames",     # 添加frames文件夹
        "--hidden-import=pynput.mouse._win32",
        "--hidden-import=pynput.keyboard._win32",
        "--hidden-import=psutil",
        "--clean",                      # 清理临时文件
        "desktop_pet_v6.py"
    ]
    
    # 如果有图标文件，添加图标参数
    if os.path.exists("cat_icon.ico"):
        cmd.insert(-1, "--icon=cat_icon.ico")
        print("🐱 使用猫咪图标")
    
    try:
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ exe文件构建成功！")
            
            # 检查生成的文件
            exe_path = "./dist/桌面宠物.exe"
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 生成文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 未找到生成的exe文件")
                return False
        else:
            print("❌ 构建失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_portable_package():
    """创建便携版本"""
    print("📦 创建便携版本...")
    
    try:
        # 创建便携版文件夹
        portable_dir = "桌面宠物_便携版"
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        os.makedirs(portable_dir)
        
        # 复制exe文件
        exe_source = "./dist/桌面宠物.exe"
        if os.path.exists(exe_source):
            shutil.copy2(exe_source, portable_dir)
            print(f"✅ 复制exe文件到 {portable_dir}")
        
        # 创建说明文件
        readme_content = """# 🐾 桌面宠物 V6.0 - 最终版本

## 🎉 功能特色
- 🎵 智能音频检测 (网易云音乐 + QQ音乐)
- 🖥️ 全屏自动隐藏/恢复
- 💬 智能对话气泡
- 🎮 丰富互动方式
- ⏰ 工作时间提醒

## 🎮 互动方式
- 🐾 左键单击 - 抚摸
- 🐟 中键点击 - 喂食  
- 🎾 双击 - 玩耍
- 💤 滚轮 - 睡觉
- 🖱️ 右键 - 菜单
- 🖱️ 拖拽 - 移动

## 🎯 三状态系统
- Work: 打字工作时显示
- Music: 播放音乐时显示
- No: 无动作时显示

## 🚀 使用方法
1. 双击"桌面宠物.exe"启动
2. 宠物会出现在屏幕中央
3. 尝试各种互动方式
4. 右键查看更多选项

## ⚠️ 注意事项
- 需要Windows 10/11系统
- 首次运行可能被杀毒软件拦截，请添加信任
- 如有问题请检查是否有网易云音乐或QQ音乐

享受与桌面宠物的互动时光！🐾✨
"""
        
        with open(os.path.join(portable_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print(f"✅ 便携版创建完成: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 便携版创建失败: {e}")
        return False

def main():
    """主函数"""
    print("🎉 桌面宠物V6.0 打包工具")
    print("=" * 50)
    
    # 检查要求
    if not check_requirements():
        print("❌ 打包要求检查失败")
        return
    
    # 创建图标
    create_icon()
    
    # 构建exe
    if not build_exe():
        print("❌ exe构建失败")
        return
    
    # 创建便携版
    create_portable_package()
    
    print("\n🎉 打包完成！")
    print("📁 生成文件:")
    print("  - ./dist/桌面宠物.exe (单文件版本)")
    print("  - ./桌面宠物_便携版/ (便携版本)")
    print("\n🚀 可以分发给其他用户使用了！")

if __name__ == "__main__":
    main()
