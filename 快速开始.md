# 🚀 桌面宠物 - 快速开始指南

## 🎯 一分钟快速启动

### 第一次使用
1. **安装依赖**: 双击 `install.bat`
2. **启动宠物**: 双击 `run_pet.bat`
3. **享受宠物**: 您的桌面宠物现在正在运行！

### 调整大小（如果觉得太大或太小）
1. **快速调整**: 双击 `调整大小.bat`
2. **选择大小**: 从菜单中选择合适的大小
3. **重新启动**: 双击 `run_pet.bat` 应用新设置

## 🎮 基本操作

| 操作 | 方法 |
|------|------|
| 移动宠物 | 左键拖拽 |
| 跳跃动画 | 双击宠物 |
| 切换动画 | 点击右上角🔄按钮 |
| 控制自动移动 | 点击🚶按钮（绿色=启用，红色=禁用） |
| 智能模式 | 点击🧠按钮（蓝色=启用，灰色=禁用） |
| 选择动画 | 右键→选择动画 |
| 隐藏宠物 | 右键→隐藏 |
| 退出程序 | 右键→退出 |

## 📁 重要文件说明

- `run_pet.bat` - 启动桌面宠物
- `调整大小.bat` - 调整宠物大小
- `install.bat` - 安装程序依赖
- `config.py` - 配置文件（高级用户）
- `frames/` - 存放GIF动画文件

## 🎨 当前动画

您的宠物有9种可爱的动画：
- 🕺 Dancing (跳舞)
- 🍽️ Eating (吃东西)
- 😆 Excited (兴奋)
- 😊 Happy (开心)
- 🦘 Jumping (跳跃)
- 🎮 Playing (玩耍)
- 😴 Sleepy (困倦)
- 🤔 Thinking (思考)
- 🚶 Walking (走路)

## 🔧 常见问题

**Q: 宠物太大了怎么办？**
A: 双击 `调整大小.bat`，选择更小的尺寸

**Q: 如何添加新的动画？**
A: 将GIF文件放入 `frames` 文件夹，重启程序

**Q: 宠物不见了怎么办？**
A: 检查任务栏，或重新运行 `run_pet.bat`

**Q: 如何完全关闭宠物？**
A: 右键点击宠物，选择"退出"

## 🎊 享受您的桌面宠物！

现在您的桌面上有了一个可爱的小伙伴，它会：
- ✨ 自动播放各种动画
- 🎯 响应您的点击和拖拽
- 🚶 每5秒自动移动位置
- 🔄 支持一键切换9种动画

祝您使用愉快！🐾
