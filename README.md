# 桌面宠物程序

一个基于PySide6/Qt的简单桌面宠物应用程序，支持GIF动画和图片序列动画。

## 功能特性

- 🎮 支持多个GIF动画切换
- 🔄 一键切换动画按钮
- 📋 右键菜单选择特定动画
- 🖱️ 可拖拽移动宠物位置
- 🎯 双击宠物执行跳跃动画
- 🚶 自动随机移动
- 📱 右键菜单（隐藏/退出/切换动画）
- 🔝 始终置顶显示
- 🌟 透明背景，无边框窗口
- 🎨 自动检测frames文件夹中的所有GIF文件

## 安装步骤

1. **安装Python**
   - 下载并安装 [Python 3.8+](https://www.python.org/downloads/)

2. **下载源代码**
   - 将项目文件下载到本地目录

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **准备动画文件**
   - 将您的GIF文件放入 `frames` 文件夹
   - 程序会自动检测所有GIF文件
   - 支持的格式：`.gif`
   - 建议文件命名格式：`pet_动作名.gif`（如：`pet_happy.gif`, `pet_dancing.gif`）

## 运行程序

```bash
python desktop_pet.py
```

## 操作说明

- **拖拽移动**: 左键按住宠物拖拽
- **跳跃动画**: 双击宠物
- **切换动画**: 点击右上角的🔄按钮
- **选择动画**: 右键点击宠物，在菜单中选择特定动画
- **右键菜单**: 右键点击宠物显示完整菜单
- **自动移动**: 宠物会每5秒自动随机移动

## 打包为可执行文件

使用PyInstaller将程序打包为独立的exe文件：

```bash
pyinstaller desktop_pet.py --onefile --windowed --add-data "frames/;frames/"
```

打包后的exe文件将在 `dist` 文件夹中。

## 自定义配置

您可以在 `desktop_pet.py` 中修改以下参数：

- `imageTimerMS`: 图片序列动画的帧间隔（毫秒）
- `move_timer.start(5000)`: 自动移动的时间间隔（毫秒）
- `jump_height = 50`: 跳跃高度（像素）
- 动画持续时间和缓动效果

## 故障排除

1. **找不到动画文件**
   - 确保文件路径正确
   - 检查文件格式是否支持

2. **程序无法启动**
   - 检查Python版本（需要3.8+）
   - 确保已安装所有依赖

3. **动画不流畅**
   - 调整帧间隔时间
   - 检查图片文件大小

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
