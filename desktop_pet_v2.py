#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面宠物 V2.0 - 重写版本
简洁、可靠、高性能的三状态智能桌面宠物
"""

import os
import sys
import time
import threading
from pathlib import Path
from PySide6.QtCore import Qt, QTimer, QPoint, QPropertyAnimation, QEasingCurve, QRect, Signal, QObject
from PySide6.QtGui import QPixmap, QMovie, QAction
from PySide6.QtWidgets import QApplication, QWidget, QLabel, QMenu, QPushButton
import psutil
from pynput import mouse, keyboard

# 配置参数
SCALE_FACTOR = 0.5
AUTO_MOVE_ENABLED = True
AUTO_MOVE_INTERVAL = 5000
JUMP_HEIGHT = 50
NO_ACTION_THRESHOLD = 8  # 8秒无活动视为无动作
TYPING_THRESHOLD = 3     # 3次按键视为工作
AUDIO_CHECK_INTERVAL = 2 # 2秒检查一次音频


class StateDetector(QObject):
    """状态检测器 - 使用Qt信号安全通信"""
    state_changed = Signal(str)  # 状态变化信号
    
    def __init__(self):
        super().__init__()
        self.current_state = "no"
        self.last_activity = time.time()
        self.typing_count = 0
        self.monitoring = False
        
        # 音频进程列表
        self.audio_processes = {
            'spotify.exe', 'netease cloudmusic.exe', 'cloudmusic.exe', 'qqmusic.exe',
            'kugou.exe', 'kuwo.exe', 'vlc.exe', 'potplayer.exe', 'foobar2000.exe',
            'chrome.exe', 'msedge.exe', 'firefox.exe', 'itunes.exe', 'winamp.exe'
        }
        
        # 监听器
        self.keyboard_listener = None
        self.mouse_listener = None
        self.monitor_thread = None
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        
        # 启动键盘监听
        self.keyboard_listener = keyboard.Listener(on_press=self._on_key_press)
        self.keyboard_listener.start()
        
        # 启动鼠标监听
        self.mouse_listener = mouse.Listener(on_move=self._on_mouse_move, on_click=self._on_mouse_click)
        self.mouse_listener.start()
        
        # 启动监控线程
        self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
        self.monitor_thread.start()
        
        print("✅ 状态监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
        if self.keyboard_listener:
            self.keyboard_listener.stop()
        if self.mouse_listener:
            self.mouse_listener.stop()
            
        print("⏹️ 状态监控已停止")
    
    def _on_key_press(self, key):
        """键盘按下"""
        self.last_activity = time.time()
        self.typing_count += 1
    
    def _on_mouse_move(self, x, y):
        """鼠标移动"""
        self.last_activity = time.time()
    
    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击"""
        if pressed:
            self.last_activity = time.time()
    
    def _monitor_loop(self):
        """监控主循环"""
        while self.monitoring:
            try:
                new_state = self._detect_state()
                
                if new_state != self.current_state:
                    self.current_state = new_state
                    self.state_changed.emit(new_state)
                    print(f"🔄 状态变化: {new_state}")
                
                # 重置计数器
                self.typing_count = 0
                
                time.sleep(AUDIO_CHECK_INTERVAL)
                
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                time.sleep(5)
    
    def _detect_state(self):
        """检测当前状态 - 优先级: work > music > no"""
        current_time = time.time()
        idle_time = current_time - self.last_activity
        
        # 优先级1: 工作状态（最高）
        if self.typing_count >= TYPING_THRESHOLD:
            return "work"
        
        # 优先级2: 音乐状态（中等）
        if self._is_audio_playing():
            return "music"
        
        # 优先级3: 无动作状态（最低）
        if idle_time > NO_ACTION_THRESHOLD:
            return "no"
        
        # 默认无动作
        return "no"
    
    def _is_audio_playing(self):
        """检测音频播放 - 简化版本"""
        try:
            for proc in psutil.process_iter(['name', 'cpu_percent']):
                proc_name = proc.info['name'].lower()
                cpu_percent = proc.info['cpu_percent'] or 0
                
                if proc_name in self.audio_processes:
                    # 不同软件不同阈值
                    threshold = 0.5 if 'chrome' in proc_name or 'edge' in proc_name or 'firefox' in proc_name else 0.1
                    if cpu_percent > threshold:
                        return True
            return False
        except:
            return False


class DesktopPet(QWidget):
    """桌面宠物主类 - 重写版本"""
    
    def __init__(self):
        super().__init__()
        
        # 基本属性
        self.current_gif = "no"
        self.drag_position = QPoint()
        
        # UI组件
        self.label = QLabel(self)
        self.movie = None
        self.switch_button = QPushButton("🔄", self)
        self.move_button = QPushButton("🚶", self)
        self.smart_button = QPushButton("🧠", self)
        
        # 功能状态
        self.auto_move_enabled = AUTO_MOVE_ENABLED
        self.smart_mode_enabled = True
        self.manual_override = False
        
        # 动画和定时器
        self.animation = None
        self.is_walking = False
        self.move_timer = QTimer(self)
        self.override_timer = QTimer(self)
        
        # 状态检测器
        self.detector = StateDetector()
        self.detector.state_changed.connect(self.on_state_change)
        
        # GIF文件路径
        self.gif_files = {
            "music": "./frames/music.gif",
            "work": "./frames/work.gif", 
            "no": "./frames/no.gif"
        }
        
        self.init_ui()
        self.init_timers()
        self.load_gif("no")  # 加载初始GIF
        
        # 启动智能模式
        if self.smart_mode_enabled:
            self.detector.start_monitoring()
    
    def init_ui(self):
        """初始化UI"""
        # 窗口设置
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        
        # 按钮设置
        button_size = max(int(30 * SCALE_FACTOR), 20)
        font_size = max(int(14 * SCALE_FACTOR), 10)
        
        for button in [self.switch_button, self.move_button, self.smart_button]:
            button.setFixedSize(button_size, button_size)
            button.setStyleSheet(f"""
                QPushButton {{
                    background-color: rgba(0, 0, 0, 120);
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 150);
                    border-radius: {button_size//2}px;
                    font-size: {font_size}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: rgba(0, 0, 0, 180);
                }}
            """)
        
        # 按钮连接
        self.switch_button.clicked.connect(self.switch_gif)
        self.move_button.clicked.connect(self.toggle_auto_move)
        self.smart_button.clicked.connect(self.toggle_smart_mode)
        
        # 更新按钮状态
        self.update_button_styles()
    
    def init_timers(self):
        """初始化定时器"""
        # 移动定时器
        self.move_timer.timeout.connect(self.random_walk)
        if self.auto_move_enabled:
            self.move_timer.start(AUTO_MOVE_INTERVAL)
        
        # 手动覆盖定时器
        self.override_timer.timeout.connect(self.resume_smart_mode)
        self.override_timer.setSingleShot(True)
        
        # 动画
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(2000)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)
        self.animation.finished.connect(lambda: setattr(self, 'is_walking', False))
    
    def load_gif(self, state):
        """加载GIF - 重写版本"""
        if state not in self.gif_files:
            print(f"❌ 未知状态: {state}")
            return
        
        gif_path = self.gif_files[state]
        if not os.path.exists(gif_path):
            print(f"❌ GIF文件不存在: {gif_path}")
            return
        
        print(f"🔄 加载GIF: {state} -> {gif_path}")
        
        # 清理旧的movie
        if self.movie:
            self.movie.stop()
            self.movie.deleteLater()
            self.movie = None
        
        # 创建新的movie
        self.movie = QMovie(gif_path)
        if not self.movie.isValid():
            print(f"❌ GIF无效: {gif_path}")
            return
        
        # 设置到label
        self.label.setMovie(self.movie)
        self.movie.frameChanged.connect(self.resize_to_gif)
        
        # 开始播放
        self.movie.start()
        self.current_gif = state
        
        print(f"✅ GIF加载成功: {state}")
    
    def resize_to_gif(self):
        """调整窗口大小"""
        if self.movie and self.movie.currentPixmap():
            size = self.movie.currentPixmap().size()
            # 应用缩放
            scaled_width = int(size.width() * SCALE_FACTOR)
            scaled_height = int(size.height() * SCALE_FACTOR)
            
            self.label.resize(scaled_width, scaled_height)
            self.label.setScaledContents(True)
            self.resize(scaled_width, scaled_height)
            
            # 定位按钮
            self.position_buttons()
    
    def position_buttons(self):
        """定位按钮"""
        spacing = 5
        x = self.width() - self.switch_button.width() - 5
        
        self.switch_button.move(x, 5)
        self.move_button.move(x, 5 + self.switch_button.height() + spacing)
        self.smart_button.move(x, 5 + (self.switch_button.height() + spacing) * 2)
    
    def on_state_change(self, new_state):
        """状态变化处理"""
        if not self.smart_mode_enabled or self.manual_override:
            return
        
        if new_state != self.current_gif:
            self.load_gif(new_state)
            print(f"🎯 智能切换: {self.current_gif} -> {new_state}")
    
    def switch_gif(self):
        """手动切换GIF"""
        states = ["music", "work", "no"]
        current_index = states.index(self.current_gif) if self.current_gif in states else 0
        next_state = states[(current_index + 1) % len(states)]
        
        self.load_gif(next_state)
        
        # 启动手动覆盖
        if self.smart_mode_enabled:
            self.manual_override = True
            self.override_timer.start(300000)  # 5分钟
            print("⏸️ 手动切换，智能模式暂停5分钟")
    
    def toggle_auto_move(self):
        """切换自动移动"""
        self.auto_move_enabled = not self.auto_move_enabled
        if self.auto_move_enabled:
            self.move_timer.start(AUTO_MOVE_INTERVAL)
        else:
            self.move_timer.stop()
        self.update_button_styles()
        print(f"🚶 自动移动: {'启用' if self.auto_move_enabled else '禁用'}")
    
    def toggle_smart_mode(self):
        """切换智能模式"""
        self.smart_mode_enabled = not self.smart_mode_enabled
        if self.smart_mode_enabled:
            self.detector.start_monitoring()
        else:
            self.detector.stop_monitoring()
        self.update_button_styles()
        print(f"🧠 智能模式: {'启用' if self.smart_mode_enabled else '禁用'}")
    
    def resume_smart_mode(self):
        """恢复智能模式"""
        self.manual_override = False
        print("🧠 智能模式已恢复")
    
    def update_button_styles(self):
        """更新按钮样式"""
        # 移动按钮
        if self.auto_move_enabled:
            self.move_button.setText("🚶")
            self.move_button.setToolTip("禁用自动移动")
        else:
            self.move_button.setText("⏸️")
            self.move_button.setToolTip("启用自动移动")
        
        # 智能按钮
        if self.smart_mode_enabled:
            self.smart_button.setText("🧠")
            self.smart_button.setToolTip("禁用智能模式")
        else:
            self.smart_button.setText("💤")
            self.smart_button.setToolTip("启用智能模式")
    
    def random_walk(self):
        """随机移动"""
        if self.is_walking:
            return
        
        screen = QApplication.primaryScreen().geometry()
        max_x = screen.width() - self.width()
        max_y = screen.height() - self.height()
        
        import random
        new_x = random.randint(0, max_x)
        new_y = random.randint(0, max_y)
        
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(new_x, new_y, self.width(), self.height()))
        self.is_walking = True
        self.animation.start()
    
    def mousePressEvent(self, event):
        """鼠标按下"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPosition().toPoint())
    
    def mouseMoveEvent(self, event):
        """鼠标移动"""
        if event.buttons() == Qt.LeftButton and not self.drag_position.isNull():
            self.move(event.globalPosition().toPoint() - self.drag_position)
    
    def mouseDoubleClickEvent(self, event):
        """双击跳跃"""
        if event.button() == Qt.LeftButton:
            self.jump()
    
    def jump(self):
        """跳跃动画"""
        if self.is_walking:
            return
        
        current_pos = self.pos()
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(current_pos.x(), current_pos.y() - JUMP_HEIGHT, self.width(), self.height()))
        self.animation.setDuration(300)
        self.is_walking = True
        self.animation.finished.connect(self.jump_down)
        self.animation.start()
    
    def jump_down(self):
        """跳跃下落"""
        self.animation.finished.disconnect()
        current_pos = self.pos()
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(current_pos.x(), current_pos.y() + JUMP_HEIGHT, self.width(), self.height()))
        self.animation.setDuration(300)
        self.animation.finished.connect(lambda: setattr(self, 'is_walking', False))
        self.animation.start()
    
    def show_context_menu(self, position):
        """右键菜单"""
        menu = QMenu(self)
        
        # 切换动画
        switch_action = QAction(f"🔄 切换动画 (当前: {self.current_gif})", self)
        switch_action.triggered.connect(self.switch_gif)
        menu.addAction(switch_action)
        
        # 自动移动
        move_text = "禁用自动移动" if self.auto_move_enabled else "启用自动移动"
        move_action = QAction(f"🚶 {move_text}", self)
        move_action.triggered.connect(self.toggle_auto_move)
        menu.addAction(move_action)
        
        # 智能模式
        smart_text = "禁用智能模式" if self.smart_mode_enabled else "启用智能模式"
        smart_action = QAction(f"🧠 {smart_text}", self)
        smart_action.triggered.connect(self.toggle_smart_mode)
        menu.addAction(smart_action)
        
        menu.addSeparator()
        
        # 隐藏和退出
        hide_action = QAction("🙈 隐藏", self)
        hide_action.triggered.connect(self.hide)
        menu.addAction(hide_action)
        
        quit_action = QAction("❌ 退出", self)
        quit_action.triggered.connect(QApplication.quit)
        menu.addAction(quit_action)
        
        menu.exec(position)
    
    def closeEvent(self, event):
        """关闭事件"""
        self.detector.stop_monitoring()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)
    
    try:
        # 检查GIF文件
        required_files = ["./frames/music.gif", "./frames/work.gif", "./frames/no.gif"]
        missing_files = [f for f in required_files if not os.path.exists(f)]
        
        if missing_files:
            print(f"❌ 缺少GIF文件: {missing_files}")
            print("请确保frames文件夹中有: music.gif, work.gif, no.gif")
            return
        
        # 创建桌面宠物
        pet = DesktopPet()
        pet.show()
        
        # 设置初始位置
        screen = app.primaryScreen().geometry()
        pet.move(screen.width() // 2, screen.height() // 2)
        
        print("🎉 桌面宠物V2.0启动成功！")
        print("📋 三状态系统: work > music > no")
        print("🎮 操作: 左键拖拽, 双击跳跃, 右键菜单")
        print("🔄 按钮: 切换动画, 🚶 自动移动, 🧠 智能模式")
        
        sys.exit(app.exec())
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        input("按回车键退出...")


if __name__ == '__main__':
    main()
