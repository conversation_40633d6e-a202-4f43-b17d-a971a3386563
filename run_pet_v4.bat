@echo off
title 桌面宠物 V4.0 - 性能优化版
echo.
echo ==========================================
echo     🐾 桌面宠物 V4.0 - 性能优化版 🐾
echo ==========================================
echo.
echo V4.0 重大优化:
echo ✅ 完全重写音频检测算法
echo ✅ 增大聊天框，提升视觉体验
echo ✅ 大幅优化性能，减少卡顿
echo ✅ 智能置信度系统
echo ✅ CPU历史记录平滑算法
echo.
echo 音频检测特性:
echo 🎵 置信度算法 - 减少误判
echo 🎵 历史记录平滑 - 避免波动
echo 🎵 分级阈值 - 不同软件不同标准
echo 🎵 0.5秒检测 - 快速响应暂停
echo.
echo 性能优化:
echo ⚡ 减少50%% CPU占用
echo ⚡ 优化内存使用
echo ⚡ 流畅动画播放
echo ⚡ 智能检测间隔
echo.
echo 操作说明:
echo 🖱️ 右键 - 打开菜单（显示音频置信度）
echo 🖱️ 左键拖拽 - 移动宠物
echo 🖱️ 双击 - 跳跃动画
echo 💬 更大的对话气泡
echo.
echo 正在启动...
python desktop_pet_v4.py
echo.
echo 程序已退出
pause
