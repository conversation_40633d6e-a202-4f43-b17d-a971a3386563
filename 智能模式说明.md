# 🧠 桌面宠物智能模式说明

## 🎯 智能模式概述

智能模式是桌面宠物的高级功能，能够自动识别您的当前活动状态，并相应地切换到最合适的动画表情。让您的桌面宠物真正成为一个贴心的智能伙伴！

## 🔧 功能特性

### 自动状态识别
- **实时监控**: 持续监控用户的键盘、鼠标活动
- **进程检测**: 识别当前运行的应用程序
- **智能判断**: 综合分析得出用户当前状态

### 智能动画切换
- **状态匹配**: 根据识别的状态自动切换相应动画
- **无缝切换**: 平滑的动画过渡效果
- **手动覆盖**: 手动切换后智能暂停5分钟

## 🎮 控制方式

### 启用/禁用智能模式
1. **按钮控制**: 点击宠物右上角的🧠按钮
2. **右键菜单**: 右键→启用/禁用智能模式
3. **状态指示**:
   - 🧠 **蓝色** = 智能模式启用
   - 💤 **灰色** = 智能模式禁用

## 📊 状态识别规则

### 🎵 音乐状态 → Dancing (跳舞)
**触发条件**:
- 检测到音乐播放软件运行
- 支持的音乐软件：
  - Spotify
  - 网易云音乐
  - QQ音乐
  - 酷狗音乐
  - 酷我音乐
  - Foobar2000
  - VLC播放器等

### ⌨️ 打字状态 → Thinking (思考)
**触发条件**:
- 2秒内检测到超过15次按键
- 通常表示正在编写文档、代码或聊天

### 💼 工作状态 → Thinking (思考)
**触发条件**:
- 检测到工作软件运行
- 支持的工作软件：
  - Visual Studio Code
  - PyCharm
  - Visual Studio
  - Office套件 (Word, Excel, PowerPoint)
  - Adobe套件 (Photoshop, Illustrator等)
  - Notepad++等

### 🎮 游戏状态 → Excited (兴奋)
**触发条件**:
- 检测到游戏平台或游戏运行
- 支持的游戏平台：
  - Steam
  - Origin
  - Epic Games
  - Battle.net
  - 各种热门游戏

### 📺 视频状态 → Happy (开心)
**触发条件**:
- 检测到视频播放软件运行
- 支持的视频软件：
  - PotPlayer
  - VLC
  - KMPlayer
  - MPC-HC等

### 😴 空闲状态 → Sleepy (困倦)
**触发条件**:
- 30秒内无键盘鼠标活动
- 表示用户暂时离开或休息

### 🚶 未知状态 → Walking (走路)
**触发条件**:
- 无法明确识别的其他状态
- 作为默认状态使用

## ⚙️ 智能设置

### 检测参数调整
在 `status_monitor.py` 中可以调整以下参数：

```python
self.idle_threshold = 30      # 空闲检测时间（秒）
self.typing_threshold = 5     # 打字检测时间（秒）
```

### 应用程序列表
可以在 `status_monitor.py` 中添加或修改应用程序列表：

```python
self.music_apps = {...}    # 音乐软件列表
self.work_apps = {...}     # 工作软件列表
self.game_apps = {...}     # 游戏软件列表
self.video_apps = {...}    # 视频软件列表
```

## 🔄 手动覆盖机制

### 覆盖触发
- 手动点击🔄按钮切换动画
- 通过右键菜单选择特定动画

### 覆盖效果
- 智能模式暂停5分钟
- 控制台显示"⏸️ 手动切换动画，智能模式暂停5分钟"
- 5分钟后自动恢复智能识别

### 覆盖时间调整
在 `config.py` 中修改：
```python
MANUAL_OVERRIDE_TIME = 300000  # 毫秒，300000 = 5分钟
```

## 🛠️ 技术实现

### 依赖库
- **psutil**: 进程监控和系统信息获取
- **pynput**: 键盘鼠标事件监听

### 监控机制
- **多线程**: 独立线程进行状态监控
- **事件驱动**: 基于回调机制的状态变化通知
- **资源优化**: 低CPU占用的高效监控

### 安全性
- **权限控制**: 仅监控必要的系统信息
- **隐私保护**: 不记录具体按键内容
- **可控制**: 随时可以禁用监控功能

## 🚀 使用建议

### 日常办公
- 启用智能模式
- 让宠物根据工作状态自动调整表情
- 提升工作时的趣味性

### 娱乐时光
- 智能模式会在听音乐时自动跳舞
- 游戏时显示兴奋表情
- 看视频时保持开心状态

### 隐私考虑
- 如果担心隐私，可以禁用智能模式
- 监控功能完全本地运行，不上传任何数据
- 可以随时通过按钮或菜单关闭

## 🔍 故障排除

### 智能模式无法启用
1. 确认已安装依赖：`pip install psutil pynput`
2. 检查是否有权限访问系统进程信息
3. 重启程序尝试

### 状态识别不准确
1. 检查应用程序是否在支持列表中
2. 可以手动添加应用程序到相应列表
3. 调整检测参数阈值

### 性能影响
- 智能模式设计为低资源占用
- 如果发现性能问题，可以临时禁用
- 监控间隔为2秒，平衡了准确性和性能

## 🎊 享受智能体验

智能模式让您的桌面宠物真正"活"了起来，它会：
- 📝 在您工作时陪您思考
- 🎵 在您听音乐时一起跳舞  
- 🎮 在您游戏时分享兴奋
- 😴 在您休息时安静陪伴

让这个智能小伙伴成为您数字生活的贴心陪伴！🐾
