# 🐾 桌面宠物使用说明

## 🎉 恭喜！您的桌面宠物已经成功运行！

### 📋 三状态智能动画系统：
1. **Music** (听歌) - music.gif
2. **Work** (工作) - work.gif
3. **No** (无动作) - no.gif

## 🎮 操作指南

### 基本操作
- **🖱️ 拖拽移动**: 左键按住宠物任意位置拖拽
- **🎯 跳跃动画**: 双击宠物执行可爱的跳跃动作
- **🔄 切换动画**: 点击宠物右上角的圆形按钮
- **🚶 控制移动**: 点击移动按钮启用/禁用自动移动
- **🧠 智能模式**: 点击智能按钮启用状态识别功能
- **📱 右键菜单**: 右键点击宠物显示完整功能菜单

### 切换动画的方法

#### 方法1: 快速切换按钮
- 点击宠物右上角的 🔄 按钮
- 每次点击会切换到下一个动画
- 按钮上会显示当前动画名称的提示

#### 方法2: 右键菜单选择
1. 右键点击宠物
2. 选择 "🔄 切换动画" 进行下一个切换
3. 或选择 "📋 选择动画" 查看所有可用动画
4. 在子菜单中点击想要的动画名称

### 控制自动移动

#### 移动控制按钮
- 点击宠物右上角的 🚶 按钮（在切换按钮下方）
- 🚶 绿色 = 自动移动启用
- ⏸️ 红色 = 自动移动禁用

#### 右键菜单控制
- 右键点击宠物
- 选择 "🚶 禁用自动移动" 或 "🚶 启用自动移动"

### 智能状态识别 🧠

#### 智能模式按钮
- 点击宠物右上角的 🧠 按钮（在移动按钮下方）
- 🧠 蓝色 = 智能模式启用
- 💤 灰色 = 智能模式禁用

#### 自动状态识别
智能模式会根据您的活动自动切换动画：
- **🎵 听音乐** → Music (music.gif)
- **⌨️ 打字工作** → Work (work.gif)
- **😴 无动作** → No (no.gif)

#### 手动覆盖
- 手动切换动画后，智能模式暂停5分钟
- 5分钟后自动恢复智能识别

### 自动功能
- **🚶 自动移动**: 宠物每5秒会自动随机移动到屏幕的其他位置（可控制开关）
- **🎬 动画循环**: GIF动画会自动循环播放
- **🔝 始终置顶**: 宠物窗口始终显示在其他程序之上

### 其他功能
- **🙈 隐藏宠物**: 右键菜单选择"隐藏"（可通过任务栏恢复）
- **❌ 退出程序**: 右键菜单选择"退出"

## 🎨 自定义设置

### 调整宠物大小
**方法1: 使用调整工具**
1. 双击 `调整大小.bat`
2. 选择想要的大小（超小/小/中等/原始/大/自定义）
3. 重新启动宠物程序

**方法2: 手动编辑配置**
1. 编辑 `config.py` 文件
2. 修改 `SCALE_FACTOR` 值：
   - `0.3` = 超小 (30%)
   - `0.5` = 小 (50%) - 默认
   - `0.7` = 中等 (70%)
   - `1.0` = 原始大小 (100%)
   - `1.2` = 大 (120%)

### 添加新的GIF动画
1. 将新的GIF文件放入 `frames` 文件夹
2. 建议命名格式：`pet_动作名.gif`
3. 重新启动程序，新动画会自动被检测

### 支持的文件格式
- ✅ GIF动画文件 (.gif)
- ✅ 透明背景GIF效果更佳
- ✅ 建议尺寸：100x100 到 300x300 像素

## 🔧 故障排除

### 常见问题
1. **宠物不显示**
   - 检查frames文件夹中是否有GIF文件
   - 确认GIF文件没有损坏

2. **动画不流畅**
   - 检查GIF文件大小（建议小于5MB）
   - 关闭其他占用CPU的程序

3. **切换按钮看不见**
   - 按钮在宠物右上角，可能被GIF内容遮挡
   - 尝试使用右键菜单切换

4. **程序无法启动**
   - 确认已安装Python和PySide6
   - 运行 `pip install -r requirements.txt`

## 🎯 高级技巧

### 快捷键操作
- **双击**: 跳跃动画
- **右键**: 显示菜单
- **左键拖拽**: 移动位置

### 个性化设置
- 修改 `desktop_pet.py` 中的参数：
  - `move_timer.start(5000)`: 修改自动移动间隔（毫秒）
  - `jump_height = 50`: 修改跳跃高度（像素）
  - 按钮样式和位置

## 🎊 享受您的桌面宠物！

您的桌面宠物现在有9种不同的动画状态，可以陪伴您的工作和学习。记得经常与它互动哦！

---
*如有问题，请检查控制台输出或重新启动程序*
