import os
import sys
from PySide6.QtCore import Qt, QTimer, QPoint, QPropertyAnimation, QEasingCurve, QRect
from PySide6.QtGui import QPixmap, QMovie, QAction
from PySide6.QtWidgets import QApp<PERSON>, QWidget, QLabel, QMenu, QPushButton, QVBoxLayout
from typing import Literal, List
from pathlib import Path
import random

# 导入配置文件
try:
    from config import *
except ImportError:
    # 如果配置文件不存在，使用默认值
    SCALE_FACTOR = 0.5
    AUTO_MOVE_ENABLED = True
    AUTO_MOVE_INTERVAL = 5000
    JUMP_HEIGHT = 50
    ANIMATION_DURATION = 2000
    JUMP_DURATION = 300
    BUTTON_MIN_SIZE = 20
    BUTTON_FONT_MIN_SIZE = 10
    SMART_MODE_ENABLED = False
    MANUAL_OVERRIDE_TIME = 300000
    NO_ACTION_THRESHOLD = 10
    TYPING_THRESHOLD = 3

# 导入状态监控模块
try:
    from status_monitor import StatusMonitor, UserState, STATE_TO_ANIMATION
    STATUS_MONITORING_AVAILABLE = True
except ImportError:
    STATUS_MONITORING_AVAILABLE = False
    print("⚠️ 智能状态识别功能不可用，请运行: pip install psutil pynput")


def resource_path(relative_path):
    """获取资源文件的绝对路径，支持打包后的exe文件"""
    if hasattr(sys, '_MEIPASS'):
        return os.path.join(sys._MEIPASS, relative_path)
    return os.path.join(os.path.abspath("."), relative_path)


class SequenceData:
    """动画序列数据类"""
    def __init__(self, files, type: Literal["image", "gif"] = "image", imageTimerMS=100) -> None:
        self.files = []
        self.type = type
        self.imageTimerMS = imageTimerMS
        
        # 转换文件路径
        for f in files:
            self.files.append(resource_path(f))
        
        print(f"加载的文件: {self.files}")
        
        if type not in ["image", "gif"]:
            raise Exception("类型必须是 'image' 或 'gif'")
        
        if len(self.files) == 0:
            raise Exception("请提供动画文件")
        
        # 验证文件存在性
        if type == "image":
            for f in self.files:
                if not os.path.exists(f):
                    raise Exception(f"图片文件不存在: {f}")
                if not Path(f).suffix.lower() in [".png", ".jpg", ".jpeg"]:
                    raise Exception("图片文件必须为 .png 或 .jpg/.jpeg 格式")
        elif type == "gif":
            if not os.path.exists(self.files[0]):
                raise Exception(f"GIF文件不存在: {self.files[0]}")
            if not Path(self.files[0]).suffix.lower() == ".gif":
                raise Exception("动画文件必须为 .gif 格式")


class GifManager:
    """GIF文件管理器"""
    def __init__(self, frames_dir="./frames"):
        self.frames_dir = frames_dir
        self.gif_files = []
        self.current_index = 0
        self.load_gif_files()

    def load_gif_files(self):
        """加载frames文件夹中的三个状态GIF文件"""
        # 只加载三个状态文件：music.gif, work.gif, no.gif
        state_files = ["music.gif", "work.gif", "no.gif"]
        self.gif_files = []

        for filename in state_files:
            file_path = os.path.join(self.frames_dir, filename)
            if os.path.exists(file_path):
                self.gif_files.append(file_path)
            else:
                print(f"⚠️ 缺少状态文件: {filename}")

        if not self.gif_files:
            raise Exception(f"在 {self.frames_dir} 文件夹中没有找到状态GIF文件 (music.gif, work.gif, no.gif)")

        print(f"加载了 {len(self.gif_files)} 个状态GIF文件:")
        for i, gif_file in enumerate(self.gif_files):
            print(f"  {i+1}. {os.path.basename(gif_file)}")

    def get_current_gif(self):
        """获取当前GIF文件路径"""
        if self.gif_files:
            return self.gif_files[self.current_index]
        return None

    def get_current_name(self):
        """获取当前GIF文件的友好名称"""
        if self.gif_files:
            filename = os.path.basename(self.gif_files[self.current_index])
            # 移除扩展名和前缀，转换为友好名称
            name = filename.replace("pet_", "").replace(".gif", "")
            return name.replace("_", " ").title()
        return "Unknown"

    def next_gif(self):
        """切换到下一个GIF"""
        if self.gif_files:
            self.current_index = (self.current_index + 1) % len(self.gif_files)
            return self.get_current_gif()
        return None

    def previous_gif(self):
        """切换到上一个GIF"""
        if self.gif_files:
            self.current_index = (self.current_index - 1) % len(self.gif_files)
            return self.get_current_gif()
        return None

    def get_gif_count(self):
        """获取GIF文件总数"""
        return len(self.gif_files)


class DesktopPet(QWidget):
    """桌面宠物主类"""
    def __init__(self, gif_manager: GifManager = None, scale_factor: float = None):
        super().__init__()
        self.gif_manager = gif_manager or GifManager()
        self.scale_factor = scale_factor if scale_factor is not None else SCALE_FACTOR
        self.current_frame = 0
        self.pet_images = []
        self.movie = None
        self.label = QLabel(self)
        self.switch_button = QPushButton("🔄", self)
        self.move_button = QPushButton("🚶", self)
        self.smart_button = QPushButton("🧠", self)
        self.drag_position = QPoint()

        # 动画相关
        self.animation = None
        self.is_walking = False
        self.auto_move_enabled = AUTO_MOVE_ENABLED  # 使用配置文件设置

        # 智能状态识别
        self.smart_mode_enabled = SMART_MODE_ENABLED
        self.status_monitor = None
        self.manual_override = False  # 手动切换动画时暂停智能模式

        self.init_ui()
        self.init_animations()
        self.load_current_gif()
        
    def init_ui(self):
        """初始化用户界面"""
        # 设置窗口属性：无边框、置顶、透明背景
        self.setWindowFlags(
            Qt.FramelessWindowHint |
            Qt.WindowStaysOnTopHint |
            Qt.Tool
        )
        self.setAttribute(Qt.WA_TranslucentBackground, True)

        # 设置切换按钮（根据缩放因子调整大小）
        button_size = int(30 * self.scale_factor) if self.scale_factor < 1.0 else 30
        button_size = max(button_size, BUTTON_MIN_SIZE)
        font_size = max(int(14 * self.scale_factor), BUTTON_FONT_MIN_SIZE)
        border_radius = button_size // 2

        self.switch_button.setFixedSize(button_size, button_size)
        self.switch_button.setStyleSheet(f"""
            QPushButton {{
                background-color: rgba(0, 0, 0, 100);
                color: white;
                border: 2px solid rgba(255, 255, 255, 150);
                border-radius: {border_radius}px;
                font-size: {font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: rgba(0, 0, 0, 150);
                border: 2px solid rgba(255, 255, 255, 200);
            }}
            QPushButton:pressed {{
                background-color: rgba(0, 0, 0, 200);
            }}
        """)
        self.switch_button.clicked.connect(self.switch_gif)
        self.switch_button.setToolTip(f"切换动画 ({self.gif_manager.get_current_name()})")

        # 设置移动控制按钮
        self.move_button.setFixedSize(button_size, button_size)
        self.move_button.setStyleSheet(f"""
            QPushButton {{
                background-color: rgba(0, 100, 0, 100);
                color: white;
                border: 2px solid rgba(255, 255, 255, 150);
                border-radius: {border_radius}px;
                font-size: {font_size}px;
                font-weight: bold;
            }}
            QPushButton:hover {{
                background-color: rgba(0, 150, 0, 150);
                border: 2px solid rgba(255, 255, 255, 200);
            }}
            QPushButton:pressed {{
                background-color: rgba(0, 200, 0, 200);
            }}
        """)
        self.move_button.clicked.connect(self.toggle_auto_move)
        self.update_move_button_tooltip()

        # 设置智能模式按钮
        self.smart_button.setFixedSize(button_size, button_size)
        self.smart_button.clicked.connect(self.toggle_smart_mode)
        self.update_smart_button_style()

        # 随机移动定时器
        self.move_timer = QTimer(self)
        self.move_timer.timeout.connect(self.random_walk)
        self.move_timer.start(AUTO_MOVE_INTERVAL)

        # 如果配置为默认启用智能模式，则自动启用
        if SMART_MODE_ENABLED and STATUS_MONITORING_AVAILABLE:
            self.smart_mode_enabled = False  # 先设为False，然后toggle会变为True
            self.toggle_smart_mode()  # 这会启用智能模式
        
    def init_animations(self):
        """初始化动画效果"""
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(ANIMATION_DURATION)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)

    def load_current_gif(self):
        """加载当前GIF文件"""
        current_gif = self.gif_manager.get_current_gif()
        if current_gif:
            # 停止之前的动画
            if self.movie:
                self.movie.stop()

            # 加载新的GIF
            self.movie = QMovie(current_gif)
            self.label.setMovie(self.movie)
            self.movie.start()
            self.movie.frameChanged.connect(self.resize_to_movie)

            # 更新按钮提示
            self.switch_button.setToolTip(f"切换动画 ({self.gif_manager.get_current_name()})")
            print(f"已切换到: {self.gif_manager.get_current_name()}")

    def switch_gif(self):
        """切换到下一个GIF"""
        self.gif_manager.next_gif()
        self.load_current_gif()

        # 手动切换时暂停智能模式
        if self.smart_mode_enabled:
            self.manual_override = True
            print("⏸️ 手动切换动画，智能模式暂停5分钟")
            # 5分钟后恢复智能模式
            QTimer.singleShot(MANUAL_OVERRIDE_TIME, self.resume_smart_mode)

        # 重新定位按钮
        self.position_switch_button()

    def resume_smart_mode(self):
        """恢复智能模式"""
        if self.smart_mode_enabled:
            self.manual_override = False
            print("🧠 智能模式已恢复")
        
    def resize_to_movie(self):
        """调整窗口大小以适应GIF动画"""
        if self.movie:
            original_size = self.movie.currentPixmap().size()
            # 应用缩放因子
            scaled_width = int(original_size.width() * self.scale_factor)
            scaled_height = int(original_size.height() * self.scale_factor)
            scaled_size = QRect(0, 0, scaled_width, scaled_height).size()

            # 设置标签大小并缩放显示
            self.label.resize(scaled_size)
            self.label.setScaledContents(True)  # 启用内容缩放
            self.resize(scaled_size)
            self.position_switch_button()

    def position_switch_button(self):
        """定位所有按钮"""
        button_spacing = 5

        # 将切换按钮放在右上角
        switch_x = self.width() - self.switch_button.width() - 5
        switch_y = 5
        self.switch_button.move(switch_x, switch_y)

        # 将移动控制按钮放在切换按钮下方
        move_x = self.width() - self.move_button.width() - 5
        move_y = switch_y + self.switch_button.height() + button_spacing
        self.move_button.move(move_x, move_y)

        # 将智能模式按钮放在移动按钮下方
        smart_x = self.width() - self.smart_button.width() - 5
        smart_y = move_y + self.move_button.height() + button_spacing
        self.smart_button.move(smart_x, smart_y)

    def toggle_auto_move(self):
        """切换自动移动状态"""
        self.auto_move_enabled = not self.auto_move_enabled
        self.update_move_button_tooltip()

        if self.auto_move_enabled:
            # 启用自动移动
            self.move_timer.start(AUTO_MOVE_INTERVAL)
            print("✅ 自动移动已启用")
        else:
            # 禁用自动移动
            self.move_timer.stop()
            print("⏸️ 自动移动已禁用")

    def update_move_button_tooltip(self):
        """更新移动按钮的提示和样式"""
        if self.auto_move_enabled:
            self.move_button.setToolTip("点击禁用自动移动")
            self.move_button.setText("🚶")
            # 绿色表示启用
            button_size = self.move_button.width()
            border_radius = button_size // 2
            font_size = max(int(14 * self.scale_factor), BUTTON_FONT_MIN_SIZE)
            self.move_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: rgba(0, 150, 0, 120);
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 150);
                    border-radius: {border_radius}px;
                    font-size: {font_size}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: rgba(0, 200, 0, 150);
                    border: 2px solid rgba(255, 255, 255, 200);
                }}
                QPushButton:pressed {{
                    background-color: rgba(0, 250, 0, 200);
                }}
            """)
        else:
            self.move_button.setToolTip("点击启用自动移动")
            self.move_button.setText("⏸️")
            # 红色表示禁用
            button_size = self.move_button.width()
            border_radius = button_size // 2
            font_size = max(int(14 * self.scale_factor), BUTTON_FONT_MIN_SIZE)
            self.move_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: rgba(150, 0, 0, 120);
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 150);
                    border-radius: {border_radius}px;
                    font-size: {font_size}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: rgba(200, 0, 0, 150);
                    border: 2px solid rgba(255, 255, 255, 200);
                }}
                QPushButton:pressed {{
                    background-color: rgba(250, 0, 0, 200);
                }}
            """)

    def toggle_smart_mode(self):
        """切换智能模式"""
        if not STATUS_MONITORING_AVAILABLE:
            print("❌ 智能模式不可用，请安装依赖: pip install psutil pynput")
            return

        self.smart_mode_enabled = not self.smart_mode_enabled
        self.update_smart_button_style()

        if self.smart_mode_enabled:
            # 启用智能模式
            if not self.status_monitor:
                self.status_monitor = StatusMonitor(callback=self.on_state_change)

            if self.status_monitor.start_monitoring():
                print("🧠 智能模式已启用")
                self.manual_override = False
            else:
                self.smart_mode_enabled = False
                self.update_smart_button_style()
        else:
            # 禁用智能模式
            if self.status_monitor:
                self.status_monitor.stop_monitoring()
            print("🧠 智能模式已禁用")

    def update_smart_button_style(self):
        """更新智能按钮样式"""
        button_size = self.smart_button.width()
        border_radius = button_size // 2
        font_size = max(int(14 * self.scale_factor), BUTTON_FONT_MIN_SIZE)

        if self.smart_mode_enabled:
            self.smart_button.setToolTip("点击禁用智能模式")
            self.smart_button.setText("🧠")
            # 蓝色表示启用
            self.smart_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: rgba(0, 100, 200, 120);
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 150);
                    border-radius: {border_radius}px;
                    font-size: {font_size}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: rgba(0, 150, 250, 150);
                    border: 2px solid rgba(255, 255, 255, 200);
                }}
                QPushButton:pressed {{
                    background-color: rgba(0, 200, 255, 200);
                }}
            """)
        else:
            self.smart_button.setToolTip("点击启用智能模式")
            self.smart_button.setText("💤")
            # 灰色表示禁用
            self.smart_button.setStyleSheet(f"""
                QPushButton {{
                    background-color: rgba(100, 100, 100, 120);
                    color: white;
                    border: 2px solid rgba(255, 255, 255, 150);
                    border-radius: {border_radius}px;
                    font-size: {font_size}px;
                    font-weight: bold;
                }}
                QPushButton:hover {{
                    background-color: rgba(150, 150, 150, 150);
                    border: 2px solid rgba(255, 255, 255, 200);
                }}
                QPushButton:pressed {{
                    background-color: rgba(200, 200, 200, 200);
                }}
            """)

    def on_state_change(self, new_state):
        """状态变化回调"""
        print(f"📡 收到状态变化: {new_state.value}")

        if not self.smart_mode_enabled:
            print(f"⏸️ 智能模式未启用，跳过切换")
            return

        if self.manual_override:
            print(f"⏸️ 手动覆盖模式，跳过切换")
            return

        # 根据状态切换动画
        target_animation = STATE_TO_ANIMATION.get(new_state, "no")
        print(f"🎯 目标动画: {target_animation}")

        # 查找对应的GIF文件（精确匹配文件名）
        found = False
        for i, gif_file in enumerate(self.gif_manager.gif_files):
            filename = os.path.basename(gif_file).lower()
            print(f"  检查文件 {i}: {filename}")
            # 精确匹配：music.gif, work.gif, no.gif
            if filename == f"{target_animation}.gif":
                print(f"  ✅ 找到匹配文件: {filename}")
                if i != self.gif_manager.current_index:
                    old_animation = self.gif_manager.get_current_name()
                    self.gif_manager.current_index = i
                    self.load_current_gif()
                    new_animation = self.gif_manager.get_current_name()
                    print(f"🎯 智能切换成功: {old_animation} -> {new_animation} ({new_state.value})")
                else:
                    print(f"  ℹ️ 已经是当前动画，无需切换")
                found = True
                break

        if not found:
            print(f"  ❌ 未找到匹配的动画文件: {target_animation}.gif")
    

    
    def random_walk(self):
        """随机移动宠物"""
        if self.is_walking or not self.auto_move_enabled:
            return
            
        screen = QApplication.primaryScreen().geometry()

        # 计算新位置，确保不超出屏幕边界
        max_x = screen.width() - self.width()
        max_y = screen.height() - self.height()
        
        new_x = random.randint(0, max_x)
        new_y = random.randint(0, max_y)
        
        # 设置动画
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(new_x, new_y, self.width(), self.height()))
        
        self.is_walking = True
        self.animation.finished.connect(self.walk_finished)
        self.animation.start()
    
    def walk_finished(self):
        """移动完成回调"""
        self.is_walking = False
        self.animation.finished.disconnect()
    
    def mousePressEvent(self, event):
        """鼠标按下事件"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPosition().toPoint())
    
    def mouseMoveEvent(self, event):
        """鼠标移动事件（拖拽）"""
        if event.buttons() == Qt.LeftButton and not self.drag_position.isNull():
            self.move(event.globalPosition().toPoint() - self.drag_position)
    
    def show_context_menu(self, position):
        """显示右键菜单"""
        menu = QMenu(self)

        # 添加GIF切换菜单
        switch_action = QAction(f"🔄 切换动画 ({self.gif_manager.get_current_name()})", self)
        switch_action.triggered.connect(self.switch_gif)
        menu.addAction(switch_action)

        # 添加移动控制菜单
        move_status = "启用" if not self.auto_move_enabled else "禁用"
        move_icon = "🚶" if not self.auto_move_enabled else "⏸️"
        move_action = QAction(f"{move_icon} {move_status}自动移动", self)
        move_action.triggered.connect(self.toggle_auto_move)
        menu.addAction(move_action)

        # 添加智能模式菜单
        if STATUS_MONITORING_AVAILABLE:
            smart_status = "禁用" if self.smart_mode_enabled else "启用"
            smart_icon = "🧠" if not self.smart_mode_enabled else "💤"
            smart_action = QAction(f"{smart_icon} {smart_status}智能模式", self)
            smart_action.triggered.connect(self.toggle_smart_mode)
            menu.addAction(smart_action)

        # 添加动画列表子菜单
        if self.gif_manager.get_gif_count() > 1:
            animations_menu = menu.addMenu("📋 选择动画")
            for i, gif_file in enumerate(self.gif_manager.gif_files):
                name = os.path.basename(gif_file).replace("pet_", "").replace(".gif", "").replace("_", " ").title()
                action = QAction(f"{'✓ ' if i == self.gif_manager.current_index else '   '}{name}", self)
                action.triggered.connect(lambda _, idx=i: self.switch_to_gif(idx))
                animations_menu.addAction(action)

        menu.addSeparator()

        # 添加其他菜单项
        hide_action = QAction("🙈 隐藏", self)
        hide_action.triggered.connect(self.hide)
        menu.addAction(hide_action)

        quit_action = QAction("❌ 退出", self)
        quit_action.triggered.connect(QApplication.quit)
        menu.addAction(quit_action)

        menu.exec(position)

    def switch_to_gif(self, index):
        """切换到指定索引的GIF"""
        self.gif_manager.current_index = index
        self.load_current_gif()

        # 手动选择时暂停智能模式
        if self.smart_mode_enabled:
            self.manual_override = True
            print("⏸️ 手动选择动画，智能模式暂停5分钟")
            QTimer.singleShot(MANUAL_OVERRIDE_TIME, self.resume_smart_mode)
    
    def mouseDoubleClickEvent(self, event):
        """双击事件"""
        if event.button() == Qt.LeftButton:
            # 双击时执行特殊动作，比如跳跃
            self.jump()
    
    def jump(self):
        """跳跃动画"""
        if self.is_walking:
            return
            
        current_pos = self.pos()
        jump_height = JUMP_HEIGHT
        
        # 向上跳跃
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(
            current_pos.x(), 
            current_pos.y() - jump_height, 
            self.width(), 
            self.height()
        ))
        self.animation.setDuration(JUMP_DURATION)
        
        self.is_walking = True
        self.animation.finished.connect(self.jump_down)
        self.animation.start()
    
    def jump_down(self):
        """跳跃下落"""
        self.animation.finished.disconnect()
        
        current_pos = self.pos()
        original_y = current_pos.y() + JUMP_HEIGHT
        
        # 向下落回
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(
            current_pos.x(), 
            original_y, 
            self.width(), 
            self.height()
        ))
        self.animation.setDuration(JUMP_DURATION)
        
        self.animation.finished.connect(self.walk_finished)
        self.animation.start()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        # 创建GIF管理器，自动加载frames文件夹中的所有GIF
        gif_manager = GifManager("./frames")

        # 创建桌面宠物（使用配置文件中的缩放设置）
        pet = DesktopPet(gif_manager)
        pet.show()

        # 设置初始位置
        screen = app.primaryScreen().geometry()
        pet.move(screen.width() // 2, screen.height() // 2)

        print(f"桌面宠物已启动！")
        print(f"当前动画: {gif_manager.get_current_name()}")
        print(f"总共有 {gif_manager.get_gif_count()} 个动画可切换")
        print(f"缩放大小: {int(SCALE_FACTOR * 100)}%")
        print("操作说明:")
        print("- 左键拖拽移动宠物")
        print("- 双击执行跳跃动画")
        print("- 点击🔄按钮切换动画")
        print("- 点击🚶按钮控制自动移动")
        if STATUS_MONITORING_AVAILABLE:
            print("- 点击🧠按钮启用智能模式（根据活动自动切换动画）")
        print("- 右键显示菜单")
        print("- 修改 config.py 文件可调整宠物大小")

        sys.exit(app.exec())

    except Exception as e:
        print(f"错误: {e}")
        input("按回车键退出...")


if __name__ == '__main__':
    main()
