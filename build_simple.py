#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包桌面宠物简化版为exe文件
解决Unicode编码问题，确保exe正常运行
"""

import os
import sys
import subprocess
import shutil

def build_simple_exe():
    """构建简化版exe文件"""
    print("🔨 构建简化版exe文件...")
    
    # 检查源文件
    if not os.path.exists("desktop_pet_simple.py"):
        print("❌ 未找到desktop_pet_simple.py")
        return False
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 无控制台窗口
        "--name=桌面宠物_简化版",        # 程序名称
        "--add-data=frames;frames",     # 添加frames文件夹
        "--hidden-import=pynput.mouse._win32",
        "--hidden-import=pynput.keyboard._win32",
        "--hidden-import=psutil",
        "--clean",                      # 清理临时文件
        "desktop_pet_simple.py"
    ]
    
    # 如果有图标文件，添加图标参数
    if os.path.exists("cat_icon.ico"):
        cmd.insert(-1, "--icon=cat_icon.ico")
        print("🐱 使用猫咪图标")
    
    try:
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 简化版exe构建成功！")
            
            # 检查生成的文件
            exe_path = "./dist/桌面宠物_简化版.exe"
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 生成文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 未找到生成的exe文件")
                return False
        else:
            print("❌ 构建失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_portable_package():
    """创建便携版本"""
    print("📦 创建便携版本...")
    
    try:
        # 创建便携版文件夹
        portable_dir = "桌面宠物_简化版_便携"
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        os.makedirs(portable_dir)
        
        # 复制exe文件
        exe_source = "./dist/桌面宠物_简化版.exe"
        if os.path.exists(exe_source):
            shutil.copy2(exe_source, portable_dir)
            print(f"✅ 复制exe文件到 {portable_dir}")
        
        # 创建说明文件
        readme_content = """# 桌面宠物 简化版

## 功能特色
- 智能音频检测 (网易云音乐 + QQ音乐)
- 智能对话气泡
- 丰富互动方式
- 工作时间提醒
- 可爱猫咪图标

## 互动方式
- 左键单击 - 抚摸 ("喵～被摸了！")
- 中键点击 - 喂食 ("谢谢小鱼干！")
- 双击 - 玩耍 ("一起玩耍！")
- 滚轮 - 睡觉 ("困了～")
- 右键 - 菜单
- 拖拽 - 移动

## 三状态系统
- Work: 打字工作时显示
- Music: 播放音乐时显示  
- No: 无动作时显示

## 使用方法
1. 双击"桌面宠物_简化版.exe"启动
2. 宠物会出现在屏幕中央
3. 尝试各种互动方式
4. 右键查看更多选项

## 注意事项
- 需要Windows 10/11系统
- 首次运行可能被杀毒软件拦截，请添加信任
- 如有问题请检查是否有网易云音乐或QQ音乐

## 更新说明
- 解决Unicode编码问题
- 去除复杂emoji字符
- 确保exe文件正常运行
- 保留所有核心功能

享受与桌面宠物的互动时光！
"""
        
        with open(os.path.join(portable_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print(f"✅ 便携版创建完成: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 便携版创建失败: {e}")
        return False

def test_exe():
    """测试exe文件"""
    print("🧪 测试exe文件...")
    
    exe_path = "./dist/桌面宠物_简化版.exe"
    if not os.path.exists(exe_path):
        print("❌ exe文件不存在")
        return False
    
    try:
        # 尝试运行exe文件（3秒后自动结束）
        print("🚀 启动exe文件测试...")
        result = subprocess.run([exe_path], timeout=3, capture_output=True, text=True)
        print("✅ exe文件可以正常启动")
        return True
        
    except subprocess.TimeoutExpired:
        print("✅ exe文件启动正常（3秒测试）")
        return True
    except Exception as e:
        print(f"❌ exe文件测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🎉 桌面宠物简化版 打包工具")
    print("=" * 50)
    print("✅ 解决Unicode编码问题")
    print("✅ 去除复杂emoji字符")
    print("✅ 确保exe正常运行")
    print("✅ 保留所有核心功能")
    print()
    
    # 构建exe
    if not build_simple_exe():
        print("❌ exe构建失败")
        return
    
    # 测试exe
    if not test_exe():
        print("⚠️ exe测试异常，但文件已生成")
    
    # 创建便携版
    create_portable_package()
    
    print("\n🎉 打包完成！")
    print("📁 生成文件:")
    print("  - ./dist/桌面宠物_简化版.exe (解决编码问题)")
    print("  - ./桌面宠物_简化版_便携/ (便携版本)")
    print("\n🚀 现在exe文件应该可以正常运行了！")
    print("✨ 简化版去除了复杂Unicode字符，确保兼容性")

if __name__ == "__main__":
    main()
