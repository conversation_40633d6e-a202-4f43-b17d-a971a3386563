#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面宠物 V6.0 - 最终版本
添加全屏检测，移除自动移动，准备打包
"""

import os
import sys
import time
import threading
from PySide6.QtCore import Qt, QTimer, QPoint, QPropertyAnimation, QEasingCurve, QRect, Signal, QObject
from PySide6.QtGui import QMovie, QAction, QPainter, QPen, QBrush, QFont, QFontMetrics
from PySide6.QtWidgets import QApplication, QWidget, QLabel, QMenu
import psutil
from pynput import mouse, keyboard

# 配置参数
SCALE_FACTOR = 0.5
JUMP_HEIGHT = 50
NO_ACTION_THRESHOLD = 10    # 10秒无活动
TYPING_THRESHOLD = 3        # 3次按键视为工作
AUDIO_CHECK_INTERVAL = 2    # 2秒检查一次音频
FULLSCREEN_CHECK_INTERVAL = 3  # 3秒检查一次全屏


class FullscreenDetector:
    """全屏检测器"""
    
    def __init__(self):
        self.is_fullscreen = False
        self.last_check_time = 0
        self.cache_duration = 2.0  # 缓存2秒
        
        # 全屏应用进程（常见的视频播放器和游戏）
        self.fullscreen_apps = {
            # 视频播放器
            'vlc.exe', 'potplayer.exe', 'kmplayer.exe', 'mpc-hc.exe',
            'mpc-be.exe', '5kplayer.exe', 'gomplayer.exe',
            
            # 浏览器（可能全屏播放视频）
            'chrome.exe', 'msedge.exe', 'firefox.exe', 'opera.exe',
            
            # 游戏平台
            'steam.exe', 'steamwebhelper.exe', 'gameoverlayui.exe',
            'epicgameslauncher.exe', 'origin.exe', 'uplay.exe',
            'battle.net.exe', 'wegame.exe',
            
            # 常见游戏
            'league of legends.exe', 'valorant.exe', 'csgo.exe',
            'dota2.exe', 'pubg.exe', 'fortnite.exe', 'minecraft.exe',
            'genshinimpact.exe', 'honkaiimpact3.exe',
            
            # 直播软件
            'obs64.exe', 'obs32.exe', 'xsplit.exe', 'streamlabs obs.exe'
        }
        
        print(f"🖥️ 全屏检测器初始化: 监控 {len(self.fullscreen_apps)} 种应用")
    
    def check_fullscreen(self):
        """检测是否有全屏应用运行"""
        current_time = time.time()
        
        # 使用缓存减少检测频率
        if current_time - self.last_check_time < self.cache_duration:
            return self.is_fullscreen
        
        self.last_check_time = current_time
        
        try:
            # 检查是否有全屏应用在运行
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if proc_name in self.fullscreen_apps:
                        self.is_fullscreen = True
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            # 使用Windows API检测真正的全屏状态
            try:
                import ctypes
                from ctypes import wintypes
                
                # 获取前台窗口
                hwnd = ctypes.windll.user32.GetForegroundWindow()
                if hwnd:
                    # 获取窗口矩形
                    rect = wintypes.RECT()
                    ctypes.windll.user32.GetWindowRect(hwnd, ctypes.byref(rect))
                    
                    # 获取屏幕尺寸
                    screen_width = ctypes.windll.user32.GetSystemMetrics(0)
                    screen_height = ctypes.windll.user32.GetSystemMetrics(1)
                    
                    # 检查窗口是否覆盖整个屏幕
                    window_width = rect.right - rect.left
                    window_height = rect.bottom - rect.top
                    
                    if (window_width >= screen_width and 
                        window_height >= screen_height and
                        rect.left <= 0 and rect.top <= 0):
                        self.is_fullscreen = True
                        return True
            except:
                pass  # Windows API调用失败时忽略
            
            self.is_fullscreen = False
            return False
            
        except Exception as e:
            print(f"❌ 全屏检测错误: {e}")
            self.is_fullscreen = False
            return False


class SimpleAudioDetector:
    """极简音频检测器"""
    
    def __init__(self):
        self.target_processes = {
            'cloudmusic.exe',           # 网易云音乐
            'netease cloudmusic.exe',   # 网易云音乐（完整名称）
            'qqmusic.exe'               # QQ音乐
        }
        
        self.last_check_time = 0
        self.cache_duration = 1.5
        self.cached_result = False
        
        print(f"🎵 音频检测器初始化: 只检测 {self.target_processes}")
    
    def is_music_running(self):
        """检测音乐软件是否运行"""
        current_time = time.time()
        
        if current_time - self.last_check_time < self.cache_duration:
            return self.cached_result
        
        self.last_check_time = current_time
        
        try:
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if proc_name in self.target_processes:
                        self.cached_result = True
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            self.cached_result = False
            return False
            
        except Exception as e:
            print(f"❌ 音频检测错误: {e}")
            self.cached_result = False
            return False


class BubbleWidget(QWidget):
    """对话气泡组件"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.message = ""
        self.hide()
        
        self.font = QFont("Microsoft YaHei", max(int(12 * SCALE_FACTOR), 11))
        self.font_metrics = QFontMetrics(self.font)
    
    def show_message(self, message, duration=4000):
        """显示消息"""
        if self.message == message and self.isVisible():
            return
            
        self.message = message
        
        text_rect = self.font_metrics.boundingRect(message)
        text_width = text_rect.width()
        text_height = text_rect.height()
        
        padding_x = 30
        padding_y = 20
        bubble_width = max(text_width + padding_x, 120)
        bubble_height = max(text_height + padding_y, 40)
        
        self.resize(bubble_width, bubble_height)
        
        if self.parent():
            parent_pos = self.parent().pos()
            parent_size = self.parent().size()
            x = parent_pos.x() + (parent_size.width() - bubble_width) // 2
            y = parent_pos.y() - bubble_height - 15
            self.move(x, y)
        
        self.show()
        QTimer.singleShot(duration, self.hide)
    
    def paintEvent(self, event):
        """绘制气泡"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        bubble_rect = QRect(5, 5, self.width() - 10, self.height() - 20)
        painter.setBrush(QBrush(Qt.white))
        painter.setPen(QPen(Qt.black, 2))
        painter.drawRoundedRect(bubble_rect, 12, 12)
        
        tail_points = [
            QPoint(self.width() // 2 - 12, self.height() - 18),
            QPoint(self.width() // 2, self.height() - 5),
            QPoint(self.width() // 2 + 12, self.height() - 18)
        ]
        painter.drawPolygon(tail_points)
        
        painter.setPen(QPen(Qt.black))
        painter.setFont(self.font)
        painter.drawText(bubble_rect, Qt.AlignCenter, self.message)


class StateDetector(QObject):
    """状态检测器"""
    state_changed = Signal(str)
    fullscreen_changed = Signal(bool)
    
    def __init__(self):
        super().__init__()
        self.current_state = "no"
        self.last_activity = time.time()
        self.typing_count = 0
        self.monitoring = False
        
        # 检测器
        self.audio_detector = SimpleAudioDetector()
        self.fullscreen_detector = FullscreenDetector()
        
        # 工作时间统计
        self.work_start_time = None
        self.work_total_time = 0
        self.last_work_reminder = time.time()
        
        # 监听器
        self.keyboard_listener = None
        self.mouse_listener = None
        self.monitor_thread = None
        
        # 全屏状态
        self.is_fullscreen = False
    
    def start_monitoring(self):
        """开始监控"""
        if self.monitoring:
            return
            
        self.monitoring = True
        
        try:
            self.keyboard_listener = keyboard.Listener(on_press=self._on_key_press)
            self.keyboard_listener.start()
            
            self.mouse_listener = mouse.Listener(on_move=self._on_mouse_move, on_click=self._on_mouse_click)
            self.mouse_listener.start()
            
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            print("✅ 状态监控已启动（包含全屏检测）")
        except Exception as e:
            print(f"❌ 监控启动失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
        try:
            if self.keyboard_listener:
                self.keyboard_listener.stop()
            if self.mouse_listener:
                self.mouse_listener.stop()
        except:
            pass
            
        print("⏹️ 状态监控已停止")
    
    def _on_key_press(self, key):
        """键盘按下"""
        self.last_activity = time.time()
        self.typing_count += 1
    
    def _on_mouse_move(self, x, y):
        """鼠标移动"""
        self.last_activity = time.time()
    
    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击"""
        if pressed:
            self.last_activity = time.time()
    
    def _monitor_loop(self):
        """监控循环"""
        fullscreen_check_counter = 0
        
        while self.monitoring:
            try:
                # 检测状态变化
                new_state = self._detect_state()
                if new_state != self.current_state:
                    if self.current_state == "work" and self.work_start_time:
                        self.work_total_time += time.time() - self.work_start_time
                        self.work_start_time = None
                    
                    if new_state == "work":
                        self.work_start_time = time.time()
                    
                    self.current_state = new_state
                    self.state_changed.emit(new_state)
                    print(f"🔄 状态变化: {new_state}")
                
                # 每3次循环检测一次全屏（6秒一次）
                fullscreen_check_counter += 1
                if fullscreen_check_counter >= 3:
                    fullscreen_check_counter = 0
                    new_fullscreen = self.fullscreen_detector.check_fullscreen()
                    if new_fullscreen != self.is_fullscreen:
                        self.is_fullscreen = new_fullscreen
                        self.fullscreen_changed.emit(new_fullscreen)
                        print(f"🖥️ 全屏状态变化: {'进入全屏' if new_fullscreen else '退出全屏'}")
                
                self.typing_count = 0
                time.sleep(AUDIO_CHECK_INTERVAL)
                
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                time.sleep(3)
    
    def _detect_state(self):
        """检测当前状态"""
        current_time = time.time()
        idle_time = current_time - self.last_activity
        
        if self.typing_count >= TYPING_THRESHOLD:
            return "work"
        
        if self.audio_detector.is_music_running():
            return "music"
        
        if idle_time > NO_ACTION_THRESHOLD:
            return "no"
        
        return "no"
    
    def get_work_time_minutes(self):
        """获取累计工作时间（分钟）"""
        total = self.work_total_time
        if self.work_start_time:
            total += time.time() - self.work_start_time
        return total / 60
    
    def should_show_work_reminder(self):
        """是否应该显示工作提醒"""
        current_time = time.time()
        work_minutes = self.get_work_time_minutes()
        
        if (work_minutes >= 30 and 
            current_time - self.last_work_reminder > 1800):
            self.last_work_reminder = current_time
            return True
        
        return False
