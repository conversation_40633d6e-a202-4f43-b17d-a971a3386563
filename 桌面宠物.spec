# -*- mode: python ; coding: utf-8 -*-


a = Analysis(
    ['desktop_pet_final.py'],
    pathex=[],
    binaries=[],
    datas=[('frames', 'frames')],
    hiddenimports=['pynput.mouse._win32', 'pynput.keyboard._win32', 'psutil'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)
pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='桌面宠物',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=['cat_icon.ico'],
)
