#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试V3.0音频检测功能
"""

import time
import subprocess
import json
import psutil

def test_powershell_audio():
    """测试PowerShell音频检测"""
    print("🔍 测试PowerShell音频检测...")
    
    try:
        cmd = '''
        Get-AudioSession | Where-Object {$_.State -eq "Active"} | 
        Select-Object ProcessName, Volume | ConvertTo-Json
        '''
        
        result = subprocess.run(
            ["powershell", "-Command", cmd],
            capture_output=True, text=True, timeout=3
        )
        
        print(f"返回码: {result.returncode}")
        print(f"输出: {result.stdout.strip()}")
        print(f"错误: {result.stderr.strip()}")
        
        if result.returncode == 0 and result.stdout.strip():
            try:
                sessions = json.loads(result.stdout)
                if isinstance(sessions, list):
                    print(f"✅ 检测到 {len(sessions)} 个活跃音频会话")
                    for session in sessions:
                        print(f"  - {session.get('ProcessName', 'Unknown')}")
                elif isinstance(sessions, dict):
                    print(f"✅ 检测到音频会话: {sessions.get('ProcessName', 'Unknown')}")
                return True
            except json.JSONDecodeError as e:
                print(f"❌ JSON解析错误: {e}")
                return False
        else:
            print("❌ 未检测到活跃音频会话")
            return False
            
    except Exception as e:
        print(f"❌ PowerShell检测失败: {e}")
        return False

def test_process_audio():
    """测试进程音频检测"""
    print("\n🔍 测试进程音频检测...")
    
    audio_processes = {
        'spotify.exe': 0.1,
        'netease cloudmusic.exe': 0.1,
        'cloudmusic.exe': 0.1,
        'qqmusic.exe': 0.1,
        'chrome.exe': 0.3,
        'msedge.exe': 0.3,
        'firefox.exe': 0.3,
        'vlc.exe': 0.1
    }
    
    found_audio = False
    
    try:
        for proc in psutil.process_iter(['name', 'cpu_percent']):
            try:
                proc_name = proc.info['name'].lower()
                cpu_percent = proc.info['cpu_percent'] or 0
                
                if proc_name in audio_processes:
                    threshold = audio_processes[proc_name]
                    status = "🎵 播放中" if cpu_percent > threshold else "⏸️ 空闲"
                    print(f"  {proc_name}: CPU {cpu_percent:.1f}% - {status}")
                    
                    if cpu_percent > threshold:
                        found_audio = True
                        
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        if found_audio:
            print("✅ 进程检测到音频播放")
        else:
            print("❌ 进程未检测到音频播放")
            
        return found_audio
        
    except Exception as e:
        print(f"❌ 进程检测失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🎵 桌面宠物V3.0 音频检测测试")
    print("=" * 50)
    
    print("请播放音乐，然后观察检测结果...")
    print("测试将进行10次，每次间隔2秒\n")
    
    for i in range(10):
        print(f"第 {i+1} 次测试:")
        
        # PowerShell检测
        ps_result = test_powershell_audio()
        
        # 进程检测
        proc_result = test_process_audio()
        
        # 综合结果
        if ps_result or proc_result:
            print("🎵 综合判断: 检测到音频播放")
        else:
            print("🔇 综合判断: 未检测到音频播放")
        
        print("-" * 30)
        
        if i < 9:  # 最后一次不等待
            time.sleep(2)
    
    print("\n🎯 测试完成！")
    print("如果播放音乐时显示'检测到音频播放'，说明检测正常")
    print("如果暂停音乐时显示'未检测到音频播放'，说明检测准确")

if __name__ == "__main__":
    main()
