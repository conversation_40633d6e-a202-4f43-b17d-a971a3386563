#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建猫咪图标
"""

from PIL import Image, ImageDraw
import os

def create_cat_icon():
    """创建一个简单的猫咪图标"""
    # 创建64x64的图像
    size = 64
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # 猫咪头部 (圆形)
    head_color = (255, 165, 0, 255)  # 橙色
    draw.ellipse([12, 16, 52, 56], fill=head_color)
    
    # 猫耳朵
    ear_points1 = [(16, 20), (24, 8), (32, 20)]
    ear_points2 = [(32, 20), (40, 8), (48, 20)]
    draw.polygon(ear_points1, fill=head_color)
    draw.polygon(ear_points2, fill=head_color)
    
    # 耳朵内部
    inner_ear_color = (255, 192, 203, 255)  # 粉色
    draw.polygon([(18, 18), (24, 12), (28, 18)], fill=inner_ear_color)
    draw.polygon([(36, 18), (40, 12), (46, 18)], fill=inner_ear_color)
    
    # 眼睛
    eye_color = (0, 0, 0, 255)  # 黑色
    draw.ellipse([20, 28, 26, 34], fill=eye_color)
    draw.ellipse([38, 28, 44, 34], fill=eye_color)
    
    # 眼睛高光
    highlight_color = (255, 255, 255, 255)  # 白色
    draw.ellipse([22, 29, 24, 31], fill=highlight_color)
    draw.ellipse([40, 29, 42, 31], fill=highlight_color)
    
    # 鼻子
    nose_color = (255, 192, 203, 255)  # 粉色
    nose_points = [(32, 36), (30, 40), (34, 40)]
    draw.polygon(nose_points, fill=nose_color)
    
    # 嘴巴
    mouth_color = (0, 0, 0, 255)  # 黑色
    draw.arc([28, 40, 36, 46], 0, 180, fill=mouth_color, width=2)
    
    # 胡须
    whisker_color = (0, 0, 0, 255)  # 黑色
    # 左边胡须
    draw.line([8, 36, 18, 34], fill=whisker_color, width=1)
    draw.line([8, 40, 18, 38], fill=whisker_color, width=1)
    # 右边胡须
    draw.line([46, 34, 56, 36], fill=whisker_color, width=1)
    draw.line([46, 38, 56, 40], fill=whisker_color, width=1)
    
    # 保存为ICO文件
    try:
        img.save('cat_icon.ico', format='ICO', sizes=[(64, 64), (32, 32), (16, 16)])
        print("✅ 猫咪图标创建成功: cat_icon.ico")
        return True
    except Exception as e:
        print(f"❌ 图标创建失败: {e}")
        return False

if __name__ == "__main__":
    create_cat_icon()
