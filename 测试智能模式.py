#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能模式功能
"""

import time
import subprocess
import os

def test_smart_mode():
    """测试智能模式功能"""
    print("🧪 智能模式功能测试")
    print("=" * 50)
    
    # 测试状态监控模块
    try:
        from status_monitor import StatusMonitor, UserState, STATE_TO_ANIMATION
        print("✅ 状态监控模块导入成功")
    except ImportError as e:
        print(f"❌ 状态监控模块导入失败: {e}")
        return
    
    # 测试状态映射
    print("\n📋 状态映射表:")
    for state, animation in STATE_TO_ANIMATION.items():
        print(f"  {state.value} -> {animation}")
    
    # 测试GIF文件匹配
    print("\n📁 检查GIF文件:")
    frames_dir = "./frames"
    gif_files = []
    
    if os.path.exists(frames_dir):
        for file in os.listdir(frames_dir):
            if file.endswith('.gif'):
                gif_files.append(file)
        
        gif_files.sort()
        for i, gif_file in enumerate(gif_files):
            print(f"  {i+1}. {gif_file}")
    else:
        print("  ❌ frames文件夹不存在")
        return
    
    # 测试动画匹配
    print("\n🔍 测试动画匹配:")
    for state, target_animation in STATE_TO_ANIMATION.items():
        found = False
        for gif_file in gif_files:
            filename = gif_file.lower()
            if target_animation in filename:
                print(f"  ✅ {state.value} -> {target_animation} -> {gif_file}")
                found = True
                break
        if not found:
            print(f"  ❌ {state.value} -> {target_animation} -> 未找到匹配文件")
    
    # 测试状态监控
    print("\n🔍 测试状态监控:")
    
    def state_callback(new_state):
        print(f"  📡 检测到状态变化: {new_state.value}")
    
    monitor = StatusMonitor(callback=state_callback)
    
    if monitor.start_monitoring():
        print("  ✅ 状态监控启动成功")
        print("  ⏳ 监控5秒钟...")
        
        # 监控5秒
        for i in range(5):
            time.sleep(1)
            current_state = monitor.get_current_state()
            print(f"  📊 当前状态: {current_state.value}")
        
        monitor.stop_monitoring()
        print("  ⏹️ 状态监控已停止")
    else:
        print("  ❌ 状态监控启动失败")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_smart_mode()
