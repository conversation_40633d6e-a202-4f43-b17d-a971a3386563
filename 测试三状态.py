#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试三状态智能模式功能
"""

import time
import os

def test_three_states():
    """测试三状态智能模式功能"""
    print("🧪 三状态智能模式测试")
    print("=" * 50)
    
    # 测试状态监控模块
    try:
        from status_monitor import StatusMonitor, UserState, STATE_TO_ANIMATION
        print("✅ 状态监控模块导入成功")
    except ImportError as e:
        print(f"❌ 状态监控模块导入失败: {e}")
        return
    
    # 显示三种状态
    print("\n📋 三种状态:")
    for state in UserState:
        print(f"  {state.value}")
    
    # 测试状态映射
    print("\n🔗 状态映射表:")
    for state, animation in STATE_TO_ANIMATION.items():
        print(f"  {state.value} -> {animation}.gif")
    
    # 检查GIF文件
    print("\n📁 检查GIF文件:")
    frames_dir = "./frames"
    
    if os.path.exists(frames_dir):
        gif_files = [f for f in os.listdir(frames_dir) if f.endswith('.gif')]
        gif_files.sort()
        
        for gif_file in gif_files:
            print(f"  📄 {gif_file}")
        
        # 验证文件匹配
        print("\n🔍 验证文件匹配:")
        for state, target_animation in STATE_TO_ANIMATION.items():
            target_file = f"{target_animation}.gif"
            if target_file in gif_files:
                print(f"  ✅ {state.value} -> {target_file} (匹配)")
            else:
                print(f"  ❌ {state.value} -> {target_file} (缺失)")
    else:
        print("  ❌ frames文件夹不存在")
        return
    
    # 测试状态监控
    print("\n🔍 测试状态监控:")
    
    def state_callback(new_state):
        print(f"  📡 状态变化: {new_state.value}")
    
    monitor = StatusMonitor(callback=state_callback)
    
    if monitor.start_monitoring():
        print("  ✅ 状态监控启动成功")
        print("  ⏳ 监控10秒钟...")
        print("  💡 提示：快速打字可以触发work状态")
        
        # 监控10秒
        for i in range(10):
            time.sleep(1)
            current_state = monitor.get_current_state()
            print(f"  📊 第{i+1}秒 - 当前状态: {current_state.value}")
        
        monitor.stop_monitoring()
        print("  ⏹️ 状态监控已停止")
    else:
        print("  ❌ 状态监控启动失败")
    
    print("\n🎯 使用说明:")
    print("1. 🎵 听音乐状态: 打开音乐软件播放音乐")
    print("2. ⌨️ 工作状态: 快速连续打字")
    print("3. 😴 无动作状态: 10秒内不操作键盘鼠标")
    
    print("\n🎉 三状态测试完成！")

if __name__ == "__main__":
    test_three_states()
