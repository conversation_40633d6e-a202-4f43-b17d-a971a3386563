# 🧠 智能模式使用指南

## ✅ 智能模式已经正常工作！

根据测试结果，智能状态识别功能完全正常。如果您觉得没有看到切换效果，可能是以下原因：

## 🎯 如何正确使用智能模式

### 1. 启用智能模式
- 点击宠物右上角的 🧠 按钮
- 按钮变为蓝色表示已启用
- 控制台会显示"🧠 智能模式已启用"

### 2. 触发状态变化
智能模式会在以下情况自动切换动画：

#### 🎵 听音乐 → Dancing (跳舞)
**如何触发**:
- 打开音乐软件：Spotify、网易云音乐、QQ音乐等
- 开始播放音乐
- 宠物会自动切换到跳舞动画

#### ⌨️ 打字工作 → Thinking (思考)  
**如何触发**:
- 快速连续打字（2秒内超过15次按键）
- 在任何文本编辑器中快速输入
- 宠物会切换到思考动画

#### 💼 办公软件 → Thinking (思考)
**如何触发**:
- 打开工作软件：VS Code、Word、Excel、PowerPoint等
- 宠物会切换到思考动画

#### 🎮 游戏 → Excited (兴奋)
**如何触发**:
- 打开游戏平台：Steam、Origin、Epic Games等
- 运行任何游戏
- 宠物会切换到兴奋动画

#### 📺 看视频 → Happy (开心)
**如何触发**:
- 打开视频播放器：PotPlayer、VLC、KMPlayer等
- 播放视频文件
- 宠物会切换到开心动画

#### 😴 空闲 → Sleepy (困倦)
**如何触发**:
- 30秒内不操作键盘和鼠标
- 宠物会自动切换到困倦动画

## 🔍 验证智能模式是否工作

### 方法1: 观察控制台输出
启动程序后，控制台会显示状态变化：
```
🔄 状态变化: gaming
🎯 智能切换: gaming -> excited
```

### 方法2: 简单测试
1. 启用智能模式（🧠按钮变蓝）
2. 打开记事本，快速连续打字
3. 观察宠物是否切换到思考动画
4. 停止打字30秒，观察是否切换到困倦动画

### 方法3: 运行测试脚本
```bash
python 测试智能模式.py
```

## ⚠️ 常见问题

### 问题1: 看不到切换效果
**可能原因**:
- 手动切换过动画，智能模式暂停5分钟
- 当前状态对应的动画正好是已显示的动画
- 应用程序不在支持列表中

**解决方法**:
- 等待5分钟后智能模式自动恢复
- 尝试不同的活动来触发状态变化
- 检查控制台是否有"🎯 智能切换"消息

### 问题2: 状态识别不准确
**可能原因**:
- 应用程序名称不在预设列表中
- 检测阈值需要调整

**解决方法**:
- 在`status_monitor.py`中添加应用程序名称
- 调整检测参数

### 问题3: 智能模式按钮是灰色的
**可能原因**:
- 缺少依赖包：psutil 或 pynput

**解决方法**:
```bash
pip install psutil pynput
```

## 🎮 实际使用场景

### 场景1: 工作时
1. 启用智能模式
2. 打开VS Code或Word
3. 宠物显示思考表情，陪您工作

### 场景2: 听音乐时
1. 启用智能模式  
2. 打开音乐软件播放音乐
3. 宠物开始跳舞，与您一起享受音乐

### 场景3: 游戏时
1. 启用智能模式
2. 打开Steam或任何游戏
3. 宠物显示兴奋表情，分享游戏乐趣

### 场景4: 休息时
1. 启用智能模式
2. 离开电脑30秒
3. 宠物自动切换到困倦状态

## 🔧 高级设置

### 自定义应用程序列表
编辑 `status_monitor.py` 文件：

```python
# 添加音乐软件
self.music_apps.add('your_music_app.exe')

# 添加工作软件  
self.work_apps.add('your_work_app.exe')

# 添加游戏
self.game_apps.add('your_game.exe')
```

### 调整检测参数
```python
self.idle_threshold = 30      # 空闲检测时间（秒）
self.typing_threshold = 5     # 打字检测时间（秒）
```

### 修改手动覆盖时间
在 `config.py` 中：
```python
MANUAL_OVERRIDE_TIME = 300000  # 5分钟，可以调整
```

## 🎉 享受智能体验

智能模式让您的桌面宠物真正理解您的活动状态，成为贴心的数字伙伴！

如果您按照指南操作后仍然遇到问题，请：
1. 检查控制台输出
2. 运行测试脚本验证功能
3. 确认依赖包已正确安装

**智能模式正在工作，让我们一起享受这个智能小伙伴吧！** 🐾✨
