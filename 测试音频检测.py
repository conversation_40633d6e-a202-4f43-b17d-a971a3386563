#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试音频检测功能
"""

import time

def test_audio_detection():
    """测试音频检测功能"""
    print("🎵 音频检测功能测试")
    print("=" * 50)
    
    try:
        from status_monitor import StatusMonitor, UserState
        print("✅ 状态监控模块导入成功")
    except ImportError as e:
        print(f"❌ 状态监控模块导入失败: {e}")
        return
    
    # 检查音频检测可用性
    try:
        import pycaw
        from pycaw.pycaw import AudioUtilities
        print("✅ 高级音频检测可用 (pycaw)")
        
        # 测试音频设备
        devices = AudioUtilities.GetSpeakers()
        if devices:
            print(f"✅ 找到音频设备: {devices}")
        else:
            print("⚠️ 未找到音频设备")
            
        # 测试音频会话
        sessions = AudioUtilities.GetAllSessions()
        print(f"📊 当前音频会话数量: {len(sessions)}")
        
        for i, session in enumerate(sessions):
            if session.Process and session.Process.name():
                print(f"  {i+1}. {session.Process.name()}")
                
    except ImportError:
        print("⚠️ 高级音频检测不可用，将使用降级检测")
    except Exception as e:
        print(f"⚠️ 音频检测初始化错误: {e}")
    
    # 创建状态监控器
    def state_callback(new_state):
        print(f"📡 状态变化: {new_state.value}")
    
    monitor = StatusMonitor(callback=state_callback)
    
    if monitor.start_monitoring():
        print("\n✅ 状态监控启动成功")
        print("🎵 请播放音乐测试音频检测...")
        print("⌨️ 请快速打字测试工作检测...")
        print("😴 请停止操作测试无动作检测...")
        print("⏳ 监控15秒钟...\n")
        
        # 监控15秒
        for i in range(15):
            time.sleep(1)
            current_state = monitor.get_current_state()
            
            # 测试音频检测
            is_audio = monitor._is_audio_playing()
            audio_status = "🎵 有音频" if is_audio else "🔇 无音频"
            
            print(f"第{i+1:2d}秒 - 状态: {current_state.value:6s} | {audio_status}")
        
        monitor.stop_monitoring()
        print("\n⏹️ 状态监控已停止")
    else:
        print("❌ 状态监控启动失败")
    
    print("\n🎯 测试说明:")
    print("1. 🎵 播放音乐时应显示 'music' 状态")
    print("2. ⌨️ 快速打字时应显示 'work' 状态")
    print("3. 😴 无操作时应显示 'no' 状态")
    print("4. 🔄 状态变化时会显示切换信息")
    
    print("\n🎉 音频检测测试完成！")

if __name__ == "__main__":
    test_audio_detection()
