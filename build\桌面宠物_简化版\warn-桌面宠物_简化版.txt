
This file lists modules PyInstaller was not able to find. This does not
necessarily mean this module is required for running your program. Python and
Python 3rd-party packages include a lot of conditional or optional modules. For
example the module 'ntpath' only exists on Windows, whereas the module
'posixpath' only exists on Posix systems.

Types if import:
* top-level: imported at the top-level - look at these first
* conditional: imported within an if-statement
* delayed: imported within a function
* optional: imported within a try-except-statement

IMPORTANT: Do NOT post this list to the issue-tracker. Use it as a basis for
            tracking down the missing module yourself. Thanks!

missing module named 'collections.abc' - imported by traceback (top-level), typing (top-level), inspect (top-level), logging (top-level), importlib.resources.readers (top-level), selectors (top-level), tracemalloc (top-level)
missing module named pwd - imported by posixpath (delayed, conditional, optional), shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional), psutil (optional)
missing module named grp - imported by shutil (delayed, optional), tarfile (optional), pathlib._local (optional), subprocess (delayed, conditional, optional)
missing module named posix - imported by posixpath (optional), shutil (conditional), importlib._bootstrap_external (conditional), os (conditional, optional)
missing module named resource - imported by posix (top-level)
missing module named _frozen_importlib_external - imported by importlib._bootstrap (delayed), importlib (optional), importlib.abc (optional)
excluded module named _frozen_importlib - imported by importlib (optional), importlib.abc (optional)
runtime module named six.moves - imported by pynput._util (top-level)
missing module named StringIO - imported by six (conditional)
missing module named 'Xlib.protocol' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.X' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.ext' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named 'Xlib.display' - imported by pynput.keyboard._xorg (top-level), pynput.mouse._xorg (top-level)
missing module named AppKit - imported by pynput.mouse._darwin (top-level)
missing module named Quartz - imported by pynput._util.darwin (top-level), pynput.keyboard._darwin (top-level), pynput.mouse._darwin (top-level)
missing module named 'Xlib.keysymdef' - imported by pynput._util.xorg (top-level), pynput.keyboard._xorg (top-level)
missing module named 'Xlib.XK' - imported by pynput._util.xorg (top-level), pynput.keyboard._xorg (top-level)
missing module named 'evdev.events' - imported by pynput.keyboard._uinput (top-level)
missing module named evdev - imported by pynput._util.uinput (top-level), pynput.keyboard._uinput (top-level)
missing module named _posixsubprocess - imported by subprocess (conditional)
missing module named fcntl - imported by subprocess (optional)
missing module named 'Xlib.threaded' - imported by pynput._util.xorg (top-level)
missing module named Xlib - imported by pynput._util.xorg (top-level)
missing module named CoreFoundation - imported by pynput._util.darwin (top-level)
missing module named HIServices - imported by pynput._util.darwin (top-level)
missing module named objc - imported by pynput._util.darwin (top-level)
