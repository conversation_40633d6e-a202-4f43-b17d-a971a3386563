import os
import glob
from pathlib import Path

def rename_gif_files():
    """自动重命名frames文件夹中的GIF文件为友好的名称"""
    frames_dir = "./frames"
    
    # 获取所有GIF文件
    gif_files = glob.glob(os.path.join(frames_dir, "*.gif"))
    gif_files.sort()  # 按文件名排序
    
    # 预定义的宠物名称
    pet_names = [
        "happy",      # 开心
        "sleepy",     # 困倦
        "excited",    # 兴奋
        "dancing",    # 跳舞
        "walking",    # 走路
        "jumping",    # 跳跃
        "playing",    # 玩耍
        "eating",     # 吃东西
        "thinking",   # 思考
        "waving",     # 挥手
        "running",    # 跑步
        "sitting",    # 坐着
        "spinning",   # 旋转
        "bouncing",   # 弹跳
        "floating"    # 漂浮
    ]
    
    renamed_files = []
    
    for i, old_file in enumerate(gif_files):
        if "mmexport" in old_file:  # 只重命名微信导出的文件
            # 使用预定义名称，如果超出范围则使用数字
            if i < len(pet_names):
                new_name = f"pet_{pet_names[i]}.gif"
            else:
                new_name = f"pet_{i+1}.gif"
            
            new_path = os.path.join(frames_dir, new_name)
            
            try:
                os.rename(old_file, new_path)
                renamed_files.append((old_file, new_path))
                print(f"重命名: {os.path.basename(old_file)} -> {new_name}")
            except Exception as e:
                print(f"重命名失败 {old_file}: {e}")
    
    print(f"\n成功重命名了 {len(renamed_files)} 个文件")
    return renamed_files

if __name__ == "__main__":
    rename_gif_files()
    input("按回车键继续...")
