#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
打包桌面宠物最终版为exe文件
滚轮抚摸，删除左键单击效果
"""

import os
import sys
import subprocess
import shutil

def build_final_exe():
    """构建最终版exe文件"""
    print("🔨 构建最终版exe文件...")
    
    # 检查源文件
    if not os.path.exists("desktop_pet_simple.py"):
        print("❌ 未找到desktop_pet_simple.py")
        return False
    
    # PyInstaller命令参数
    cmd = [
        "pyinstaller",
        "--onefile",                    # 打包成单个exe文件
        "--windowed",                   # 无控制台窗口
        "--name=桌面宠物_最终版",        # 程序名称
        "--add-data=frames;frames",     # 添加frames文件夹
        "--hidden-import=pynput.mouse._win32",
        "--hidden-import=pynput.keyboard._win32",
        "--hidden-import=psutil",
        "--clean",                      # 清理临时文件
        "desktop_pet_simple.py"
    ]
    
    # 如果有图标文件，添加图标参数
    if os.path.exists("cat_icon.ico"):
        cmd.insert(-1, "--icon=cat_icon.ico")
        print("🐱 使用猫咪图标")
    
    try:
        print("执行命令:", " ".join(cmd))
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 最终版exe构建成功！")
            
            # 检查生成的文件
            exe_path = "./dist/桌面宠物_最终版.exe"
            if os.path.exists(exe_path):
                file_size = os.path.getsize(exe_path) / (1024 * 1024)  # MB
                print(f"📁 生成文件: {exe_path}")
                print(f"📊 文件大小: {file_size:.1f} MB")
                return True
            else:
                print("❌ 未找到生成的exe文件")
                return False
        else:
            print("❌ 构建失败:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ 构建过程出错: {e}")
        return False

def create_final_package():
    """创建最终版便携包"""
    print("📦 创建最终版便携包...")
    
    try:
        # 创建便携版文件夹
        portable_dir = "桌面宠物_最终版_便携"
        if os.path.exists(portable_dir):
            shutil.rmtree(portable_dir)
        os.makedirs(portable_dir)
        
        # 复制exe文件
        exe_source = "./dist/桌面宠物_最终版.exe"
        if os.path.exists(exe_source):
            shutil.copy2(exe_source, portable_dir)
            print(f"✅ 复制exe文件到 {portable_dir}")
        
        # 复制frames文件夹
        if os.path.exists("frames"):
            shutil.copytree("frames", os.path.join(portable_dir, "frames"))
            print("✅ 复制frames文件夹")
        
        # 创建说明文件
        readme_content = """# 桌面宠物 最终版

## 功能特色
- 智能音频检测 (网易云音乐 + QQ音乐)
- 智能对话气泡
- 优化的互动方式
- 工作时间提醒
- 可爱猫咪图标

## 互动方式 (已优化)
- 滚轮 - 抚摸 ("喵～被摸了！")
- 中键点击 - 喂食 ("谢谢小鱼干！")
- 双击 - 玩耍 ("一起玩耍！")
- 右键 - 菜单
- 拖拽 - 移动

## 更新说明
- ✅ 滚轮改为抚摸功能
- ✅ 删除左键单击效果
- ✅ 左键专用于拖拽移动
- ✅ 优化交互体验

## 三状态系统
- Work: 打字工作时显示
- Music: 播放音乐时显示  
- No: 无动作时显示

## 使用方法
1. 双击"桌面宠物_最终版.exe"启动
2. 宠物会出现在屏幕中央
3. 尝试各种互动方式
4. 右键查看更多选项

## 注意事项
- 需要Windows 10/11系统
- 首次运行可能被杀毒软件拦截，请添加信任
- 如有问题请检查是否有网易云音乐或QQ音乐

享受与桌面宠物的互动时光！
"""
        
        with open(os.path.join(portable_dir, "使用说明.txt"), "w", encoding="utf-8") as f:
            f.write(readme_content)
        
        print(f"✅ 最终版便携包创建完成: {portable_dir}")
        return True
        
    except Exception as e:
        print(f"❌ 便携包创建失败: {e}")
        return False

def clean_old_files():
    """清理旧版本文件"""
    print("🧹 清理旧版本文件...")
    
    old_files = [
        "./dist/桌面宠物.exe",
        "./dist/桌面宠物_调试版.exe",
        "./桌面宠物_简化版.exe"
    ]
    
    for file in old_files:
        if os.path.exists(file):
            try:
                os.remove(file)
                print(f"✅ 删除旧文件: {file}")
            except Exception as e:
                print(f"⚠️ 无法删除 {file}: {e}")

def main():
    """主函数"""
    print("🎉 桌面宠物最终版 打包工具")
    print("=" * 50)
    print("✅ 滚轮改为抚摸功能")
    print("✅ 删除左键单击效果")
    print("✅ 优化交互体验")
    print("✅ 生成最终版exe")
    print()
    
    # 构建exe
    if not build_final_exe():
        print("❌ exe构建失败")
        return
    
    # 创建便携包
    create_final_package()
    
    # 清理旧文件
    clean_old_files()
    
    print("\n🎉 最终版打包完成！")
    print("📁 生成文件:")
    print("  - ./dist/桌面宠物_最终版.exe (最终版本)")
    print("  - ./桌面宠物_最终版_便携/ (便携版本)")
    print("\n🎮 新的交互方式:")
    print("  - 滚轮 - 抚摸")
    print("  - 中键 - 喂食")
    print("  - 双击 - 玩耍")
    print("  - 右键 - 菜单")
    print("  - 拖拽 - 移动")
    print("\n🚀 最终版本可以正常使用了！")

if __name__ == "__main__":
    main()
