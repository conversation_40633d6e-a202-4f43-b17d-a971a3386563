@echo off
title 桌面宠物 V6.0 - 最终版本
echo.
echo ==========================================
echo     🐾 桌面宠物 V6.0 - 最终版本 🐾
echo ==========================================
echo.
echo V6.0 最终特性:
echo ✅ 全屏检测 - 全屏时自动隐藏，退出时恢复
echo ✅ 移除自动移动 - 专注互动体验
echo ✅ 丰富互动方式 - 5种互动方式
echo ✅ 智能对话系统 - 互动时显示对话
echo ✅ 猫咪应用图标 - 可爱的猫咪图标
echo ✅ 准备打包exe - 可分发给其他用户
echo.
echo 🎮 互动方式:
echo 🐾 左键单击 - 抚摸 (喵～被摸了！)
echo 🐟 中键点击 - 喂食 (谢谢小鱼干！)
echo 🎾 双击 - 玩耍 (一起玩耍！)
echo 💤 滚轮 - 睡觉 (困了～)
echo 🖱️ 右键 - 菜单
echo 🖱️ 拖拽 - 移动
echo.
echo 🖥️ 全屏检测:
echo 📺 播放全屏视频时自动隐藏
echo 🎮 运行全屏游戏时自动隐藏
echo 👁️ 退出全屏时自动恢复显示
echo.
echo 🎵 音频检测:
echo 🎶 网易云音乐 (cloudmusic.exe)
echo 🎶 QQ音乐 (qqmusic.exe)
echo 🎶 软件启动即切换music状态
echo.
echo 💬 智能对话:
echo 💝 根据状态显示相关消息
echo 💝 互动时显示对应对话
echo 💝 工作30分钟提醒休息
echo.
echo 正在启动最终版本...
python desktop_pet_v6.py
echo.
echo 程序已退出
pause
