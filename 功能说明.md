# 🐾 桌面宠物完整功能说明

## 🎮 控制按钮

### 右上角按钮区域
您的桌面宠物右上角有两个小按钮：

#### 🔄 动画切换按钮（上方）
- **功能**: 切换到下一个动画
- **颜色**: 黑色背景
- **提示**: 悬停显示当前动画名称
- **操作**: 单击切换

#### 🚶 移动控制按钮（下方）
- **功能**: 控制自动移动开关
- **状态指示**:
  - 🚶 **绿色** = 自动移动启用
  - ⏸️ **红色** = 自动移动禁用
- **操作**: 单击切换状态

## 🖱️ 鼠标操作

### 左键操作
- **单击拖拽**: 移动宠物到任意位置
- **双击**: 执行跳跃动画

### 右键操作
- **右键菜单**: 显示完整功能菜单
  - 🔄 切换动画
  - 🚶/⏸️ 启用/禁用自动移动
  - 📋 选择动画（子菜单）
  - 🙈 隐藏宠物
  - ❌ 退出程序

## 🎬 动画系统

### 9种可爱动画
1. **Dancing** 🕺 - 欢快的跳舞
2. **Eating** 🍽️ - 可爱的进食
3. **Excited** 😆 - 兴奋激动
4. **Happy** 😊 - 开心愉悦
5. **Jumping** 🦘 - 活泼跳跃
6. **Playing** 🎮 - 玩耍嬉戏
7. **Sleepy** 😴 - 困倦想睡
8. **Thinking** 🤔 - 思考沉思
9. **Walking** 🚶 - 悠闲行走

### 切换方式
- **快速切换**: 点击🔄按钮
- **精确选择**: 右键菜单→选择动画

## 🚶 移动系统

### 自动移动
- **默认状态**: 启用
- **移动间隔**: 每5秒
- **移动范围**: 整个屏幕
- **边界检测**: 自动避免超出屏幕

### 手动移动
- **拖拽移动**: 随时可用
- **跳跃动画**: 双击触发

### 移动控制
- **按钮控制**: 点击🚶按钮
- **菜单控制**: 右键菜单选择
- **实时反馈**: 控制台显示状态

## ⚙️ 配置系统

### 大小调整
- **工具**: `调整大小.bat`
- **选项**: 30%、50%、70%、100%、120%
- **自定义**: 0.1-2.0倍任意缩放

### 配置文件 (`config.py`)
```python
SCALE_FACTOR = 0.5        # 缩放大小
AUTO_MOVE_ENABLED = True  # 自动移动
AUTO_MOVE_INTERVAL = 5000 # 移动间隔
JUMP_HEIGHT = 50          # 跳跃高度
```

## 🎯 高级功能

### 窗口特性
- **无边框**: 完美融入桌面
- **透明背景**: 只显示宠物本体
- **始终置顶**: 不被其他窗口遮挡
- **自适应大小**: 根据GIF尺寸调整

### 智能交互
- **状态记忆**: 记住移动开关状态
- **平滑动画**: 流畅的移动和跳跃
- **视觉反馈**: 按钮悬停和点击效果
- **工具提示**: 详细的操作说明

## 🔧 技术特点

### 性能优化
- **高效渲染**: Qt原生GIF支持
- **内存管理**: 智能资源释放
- **CPU友好**: 低资源占用

### 扩展性
- **模块化设计**: 易于添加新功能
- **配置驱动**: 灵活的参数调整
- **文件检测**: 自动发现新动画

## 📱 用户体验

### 直观操作
- **可视化按钮**: 清晰的功能指示
- **即时反馈**: 操作结果立即显示
- **状态提示**: 详细的工具提示

### 个性化
- **大小自定义**: 适应不同屏幕
- **行为控制**: 自由选择移动模式
- **动画选择**: 丰富的表情状态

## 🎊 使用建议

### 日常使用
- 保持自动移动启用，增加趣味性
- 根据工作需要调整宠物大小
- 使用不同动画表达心情

### 工作场景
- 禁用自动移动，避免干扰
- 选择安静的动画（如Thinking）
- 调整到较小尺寸

### 娱乐场景
- 启用所有功能
- 频繁切换动画
- 与宠物互动（拖拽、双击）

---

**享受您的桌面宠物伙伴！** 🐾
