#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面宠物大小调整工具
"""

import os

def update_config(scale_factor, auto_move_enabled=True):
    """更新配置文件中的缩放因子和自动移动设置"""
    config_content = f'''# 桌面宠物配置文件

# 宠物大小设置
# 缩放因子：1.0 = 原始大小，0.5 = 缩小到50%，0.3 = 缩小到30%
SCALE_FACTOR = {scale_factor}

# 预设大小选项（取消注释想要的大小）
# SCALE_FACTOR = 0.3   # 超小 (30%)
# SCALE_FACTOR = 0.5   # 小 (50%)
# SCALE_FACTOR = 0.7   # 中等 (70%)
# SCALE_FACTOR = 1.0   # 原始大小 (100%)
# SCALE_FACTOR = 1.2   # 大 (120%)

# 移动设置
AUTO_MOVE_ENABLED = {auto_move_enabled}   # 是否默认启用自动移动
AUTO_MOVE_INTERVAL = 5000  # 自动移动间隔（毫秒），5000 = 5秒
JUMP_HEIGHT = 50          # 跳跃高度（像素）

# 动画设置
ANIMATION_DURATION = 2000  # 移动动画持续时间（毫秒）
JUMP_DURATION = 300       # 跳跃动画持续时间（毫秒）

# 按钮设置
BUTTON_MIN_SIZE = 20      # 按钮最小尺寸（像素）
BUTTON_FONT_MIN_SIZE = 10 # 按钮字体最小尺寸（像素）
'''
    
    with open('config.py', 'w', encoding='utf-8') as f:
        f.write(config_content)
    
    print(f"✅ 已将宠物大小设置为 {int(scale_factor * 100)}%")

def main():
    print("🐾 桌面宠物大小调整工具")
    print("=" * 40)
    print("请选择宠物大小：")
    print("1. 超小 (30%)")
    print("2. 小 (50%) - 当前设置")
    print("3. 中等 (70%)")
    print("4. 原始大小 (100%)")
    print("5. 大 (120%)")
    print("6. 自定义大小")
    print("0. 退出")
    print("=" * 40)
    
    while True:
        try:
            choice = input("请输入选项 (0-6): ").strip()
            
            if choice == '0':
                print("👋 再见！")
                break
            elif choice == '1':
                update_config(0.3)
                break
            elif choice == '2':
                update_config(0.5)
                break
            elif choice == '3':
                update_config(0.7)
                break
            elif choice == '4':
                update_config(1.0)
                break
            elif choice == '5':
                update_config(1.2)
                break
            elif choice == '6':
                while True:
                    try:
                        custom = input("请输入自定义缩放比例 (0.1-2.0): ").strip()
                        scale = float(custom)
                        if 0.1 <= scale <= 2.0:
                            update_config(scale)
                            break
                        else:
                            print("❌ 请输入 0.1 到 2.0 之间的数值")
                    except ValueError:
                        print("❌ 请输入有效的数字")
                break
            else:
                print("❌ 无效选项，请重新输入")
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
    
    print("\n💡 提示：重新启动桌面宠物程序以应用新设置")
    input("按回车键退出...")

if __name__ == "__main__":
    main()
