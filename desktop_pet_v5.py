#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
桌面宠物 V5.0 - 极简高性能版本
只检测网易云和QQ音乐，大幅优化性能
"""

import os
import sys
import time
import threading
from PySide6.QtCore import Qt, QTimer, QPoint, QPropertyAnimation, QEasingCurve, QRect, Signal, QObject
from PySide6.QtGui import QMovie, QAction, QPainter, QPen, QBrush, QFont, QFontMetrics
from PySide6.QtWidgets import QApplication, QWidget, QLabel, QMenu
import psutil
from pynput import mouse, keyboard

# 配置参数 - 性能优化
SCALE_FACTOR = 0.5
AUTO_MOVE_ENABLED = True
AUTO_MOVE_INTERVAL = 15000  # 15秒移动一次，减少动画频率
JUMP_HEIGHT = 50
NO_ACTION_THRESHOLD = 10    # 10秒无活动
TYPING_THRESHOLD = 3        # 3次按键视为工作
AUDIO_CHECK_INTERVAL = 2    # 2秒检查一次（降低频率）


class SimpleAudioDetector:
    """极简音频检测器 - 只检测网易云和QQ音乐"""
    
    def __init__(self):
        # 只检测这两个音乐软件
        self.target_processes = {
            'cloudmusic.exe',           # 网易云音乐
            'netease cloudmusic.exe',   # 网易云音乐（完整名称）
            'qqmusic.exe'               # QQ音乐
        }
        
        # 缓存机制 - 减少检测频率
        self.last_check_time = 0
        self.cache_duration = 1.5  # 缓存1.5秒
        self.cached_result = False
        
        print(f"🎵 音频检测器初始化: 只检测 {self.target_processes}")
    
    def is_music_running(self):
        """检测音乐软件是否运行 - 极简版本"""
        current_time = time.time()
        
        # 使用缓存减少检测频率
        if current_time - self.last_check_time < self.cache_duration:
            return self.cached_result
        
        self.last_check_time = current_time
        
        try:
            # 只检查进程是否存在，不检查CPU
            for proc in psutil.process_iter(['name']):
                try:
                    proc_name = proc.info['name'].lower()
                    if proc_name in self.target_processes:
                        self.cached_result = True
                        return True
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    continue
            
            self.cached_result = False
            return False
            
        except Exception as e:
            print(f"❌ 音频检测错误: {e}")
            self.cached_result = False
            return False


class BubbleWidget(QWidget):
    """优化的对话气泡 - 减少重绘"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
        self.message = ""
        self.hide()
        
        # 预计算字体
        self.font = QFont("Microsoft YaHei", max(int(12 * SCALE_FACTOR), 11))
        self.font_metrics = QFontMetrics(self.font)
    
    def show_message(self, message, duration=4000):
        """显示消息 - 优化版本"""
        if self.message == message and self.isVisible():
            return  # 避免重复显示相同消息
            
        self.message = message
        
        # 使用预计算的字体
        text_rect = self.font_metrics.boundingRect(message)
        text_width = text_rect.width()
        text_height = text_rect.height()
        
        # 计算气泡尺寸
        padding_x = 30
        padding_y = 20
        bubble_width = max(text_width + padding_x, 120)
        bubble_height = max(text_height + padding_y, 40)
        
        self.resize(bubble_width, bubble_height)
        
        # 定位到宠物上方
        if self.parent():
            parent_pos = self.parent().pos()
            parent_size = self.parent().size()
            x = parent_pos.x() + (parent_size.width() - bubble_width) // 2
            y = parent_pos.y() - bubble_height - 15
            self.move(x, y)
        
        self.show()
        QTimer.singleShot(duration, self.hide)
    
    def paintEvent(self, event):
        """绘制气泡 - 优化版本"""
        painter = QPainter(self)
        painter.setRenderHint(QPainter.Antialiasing)
        
        # 绘制气泡背景
        bubble_rect = QRect(5, 5, self.width() - 10, self.height() - 20)
        painter.setBrush(QBrush(Qt.white))
        painter.setPen(QPen(Qt.black, 2))
        painter.drawRoundedRect(bubble_rect, 12, 12)
        
        # 绘制小尾巴
        tail_points = [
            QPoint(self.width() // 2 - 12, self.height() - 18),
            QPoint(self.width() // 2, self.height() - 5),
            QPoint(self.width() // 2 + 12, self.height() - 18)
        ]
        painter.drawPolygon(tail_points)
        
        # 绘制文字
        painter.setPen(QPen(Qt.black))
        painter.setFont(self.font)
        painter.drawText(bubble_rect, Qt.AlignCenter, self.message)


class StateDetector(QObject):
    """极简状态检测器 - 性能优化"""
    state_changed = Signal(str)
    
    def __init__(self):
        super().__init__()
        self.current_state = "no"
        self.last_activity = time.time()
        self.typing_count = 0
        self.monitoring = False
        
        # 极简音频检测器
        self.audio_detector = SimpleAudioDetector()
        
        # 工作时间统计
        self.work_start_time = None
        self.work_total_time = 0
        self.last_work_reminder = time.time()
        
        # 性能优化：减少监听器开销
        self.keyboard_listener = None
        self.mouse_listener = None
        self.monitor_thread = None
        
        # 状态缓存
        self.last_state_check = 0
        self.state_cache_duration = 1.0  # 状态缓存1秒
    
    def start_monitoring(self):
        """开始监控 - 优化版本"""
        if self.monitoring:
            return
            
        self.monitoring = True
        
        try:
            # 启动输入监听
            self.keyboard_listener = keyboard.Listener(on_press=self._on_key_press)
            self.keyboard_listener.start()
            
            self.mouse_listener = mouse.Listener(on_move=self._on_mouse_move, on_click=self._on_mouse_click)
            self.mouse_listener.start()
            
            # 启动监控线程
            self.monitor_thread = threading.Thread(target=self._monitor_loop, daemon=True)
            self.monitor_thread.start()
            
            print("✅ 极简状态监控已启动")
        except Exception as e:
            print(f"❌ 监控启动失败: {e}")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        
        try:
            if self.keyboard_listener:
                self.keyboard_listener.stop()
            if self.mouse_listener:
                self.mouse_listener.stop()
        except:
            pass
            
        print("⏹️ 状态监控已停止")
    
    def _on_key_press(self, key):
        """键盘按下"""
        self.last_activity = time.time()
        self.typing_count += 1
    
    def _on_mouse_move(self, x, y):
        """鼠标移动"""
        self.last_activity = time.time()
    
    def _on_mouse_click(self, x, y, button, pressed):
        """鼠标点击"""
        if pressed:
            self.last_activity = time.time()
    
    def _monitor_loop(self):
        """极简监控循环 - 大幅优化性能"""
        while self.monitoring:
            try:
                new_state = self._detect_state()
                
                if new_state != self.current_state:
                    # 处理工作时间统计
                    if self.current_state == "work" and self.work_start_time:
                        self.work_total_time += time.time() - self.work_start_time
                        self.work_start_time = None
                    
                    if new_state == "work":
                        self.work_start_time = time.time()
                    
                    self.current_state = new_state
                    self.state_changed.emit(new_state)
                    print(f"🔄 状态变化: {new_state}")
                
                # 重置计数器
                self.typing_count = 0
                
                # 降低检测频率，减少CPU占用
                time.sleep(AUDIO_CHECK_INTERVAL)
                
            except Exception as e:
                print(f"❌ 监控错误: {e}")
                time.sleep(3)
    
    def _detect_state(self):
        """极简状态检测"""
        current_time = time.time()
        idle_time = current_time - self.last_activity
        
        # 优先级1: 工作状态（最高）
        if self.typing_count >= TYPING_THRESHOLD:
            return "work"
        
        # 优先级2: 音乐状态（中等）- 只检测网易云和QQ音乐
        if self.audio_detector.is_music_running():
            return "music"
        
        # 优先级3: 无动作状态（最低）
        if idle_time > NO_ACTION_THRESHOLD:
            return "no"
        
        return "no"
    
    def get_work_time_minutes(self):
        """获取累计工作时间（分钟）"""
        total = self.work_total_time
        if self.work_start_time:
            total += time.time() - self.work_start_time
        return total / 60
    
    def should_show_work_reminder(self):
        """是否应该显示工作提醒"""
        current_time = time.time()
        work_minutes = self.get_work_time_minutes()
        
        if (work_minutes >= 30 and 
            current_time - self.last_work_reminder > 1800):  # 30分钟
            self.last_work_reminder = current_time
            return True
        
        return False


class DesktopPet(QWidget):
    """桌面宠物主类 - V5.0 极简高性能版"""
    
    def __init__(self):
        super().__init__()
        
        # 基本属性
        self.current_gif = "no"
        self.drag_position = QPoint()
        
        # UI组件
        self.label = QLabel(self)
        self.movie = None
        self.bubble = BubbleWidget(self)
        
        # 功能状态
        self.auto_move_enabled = AUTO_MOVE_ENABLED
        self.smart_mode_enabled = True
        self.manual_override = False
        
        # 动画和定时器
        self.animation = None
        self.is_walking = False
        self.move_timer = QTimer(self)
        self.override_timer = QTimer(self)
        self.reminder_timer = QTimer(self)
        
        # 状态检测器
        self.detector = StateDetector()
        self.detector.state_changed.connect(self.on_state_change)
        
        # GIF文件路径
        self.gif_files = {
            "music": "./frames/music.gif",
            "work": "./frames/work.gif", 
            "no": "./frames/no.gif"
        }
        
        # 精简对话消息
        self.messages = {
            "work": [
                "专注工作中！💪",
                "保持专注～✨",
                "工作状态很棒！📝",
                "继续加油！⚡"
            ],
            "music": [
                "享受音乐时光～🎵",
                "音乐真好听！🎶",
                "放松一下～😌",
                "跟着节拍摇摆！🎧"
            ],
            "no": [
                "休息一下吧～😴",
                "适当休息很重要！🌸",
                "静静陪着你～🐾",
                "发呆也不错～💭"
            ],
            "work_reminder": [
                "工作30分钟了，休息一下眼睛！👀",
                "该活动活动了～🚶‍♂️",
                "喝口水放松一下！💧",
                "适当休息效率更高！⏰"
            ]
        }
        
        self.init_ui()
        self.init_timers()
        self.load_gif("no")
        
        # 启动智能模式
        if self.smart_mode_enabled:
            self.detector.start_monitoring()
    
    def init_ui(self):
        """初始化UI"""
        self.setWindowFlags(Qt.FramelessWindowHint | Qt.WindowStaysOnTopHint | Qt.Tool)
        self.setAttribute(Qt.WA_TranslucentBackground, True)
    
    def init_timers(self):
        """初始化定时器 - 优化性能"""
        # 移动定时器 - 降低频率
        self.move_timer.timeout.connect(self.random_walk)
        if self.auto_move_enabled:
            self.move_timer.start(AUTO_MOVE_INTERVAL)
        
        # 手动覆盖定时器
        self.override_timer.timeout.connect(self.resume_smart_mode)
        self.override_timer.setSingleShot(True)
        
        # 提醒定时器 - 降低频率
        self.reminder_timer.timeout.connect(self.check_reminders)
        self.reminder_timer.start(20000)  # 每20秒检查一次
        
        # 动画
        self.animation = QPropertyAnimation(self, b"geometry")
        self.animation.setDuration(2000)
        self.animation.setEasingCurve(QEasingCurve.InOutQuad)
        self.animation.finished.connect(lambda: setattr(self, 'is_walking', False))
    
    def load_gif(self, state):
        """优化的GIF加载"""
        if state not in self.gif_files or state == self.current_gif:
            return  # 避免重复加载
        
        gif_path = self.gif_files[state]
        if not os.path.exists(gif_path):
            print(f"❌ GIF文件不存在: {gif_path}")
            return
        
        # 清理旧的movie
        if self.movie:
            self.movie.stop()
            self.movie.deleteLater()
            self.movie = None
        
        # 创建新的movie
        self.movie = QMovie(gif_path)
        if not self.movie.isValid():
            return
        
        # 设置到label
        self.label.setMovie(self.movie)
        self.movie.frameChanged.connect(self.resize_to_gif)
        self.movie.start()
        self.current_gif = state
        
        print(f"✅ GIF加载: {state}")
    
    def resize_to_gif(self):
        """调整窗口大小"""
        if self.movie and self.movie.currentPixmap():
            size = self.movie.currentPixmap().size()
            scaled_width = int(size.width() * SCALE_FACTOR)
            scaled_height = int(size.height() * SCALE_FACTOR)
            
            self.label.resize(scaled_width, scaled_height)
            self.label.setScaledContents(True)
            self.resize(scaled_width, scaled_height)
    
    def on_state_change(self, new_state):
        """状态变化处理"""
        if not self.smart_mode_enabled or self.manual_override:
            return
        
        if new_state != self.current_gif:
            self.load_gif(new_state)
            
            # 显示状态消息
            import random
            if new_state in self.messages:
                message = random.choice(self.messages[new_state])
                self.bubble.show_message(message)
            
            print(f"🎯 智能切换: {self.current_gif} -> {new_state}")
    
    def check_reminders(self):
        """检查提醒"""
        if self.detector.should_show_work_reminder():
            import random
            message = random.choice(self.messages["work_reminder"])
            self.bubble.show_message(message, 6000)
            
            work_minutes = int(self.detector.get_work_time_minutes())
            print(f"⏰ 工作提醒: 已工作{work_minutes}分钟")
    
    def switch_gif(self):
        """手动切换GIF"""
        states = ["music", "work", "no"]
        current_index = states.index(self.current_gif) if self.current_gif in states else 0
        next_state = states[(current_index + 1) % len(states)]
        
        self.load_gif(next_state)
        
        # 启动手动覆盖
        if self.smart_mode_enabled:
            self.manual_override = True
            self.override_timer.start(300000)  # 5分钟
            print("⏸️ 手动切换，智能模式暂停5分钟")
    
    def toggle_auto_move(self):
        """切换自动移动"""
        self.auto_move_enabled = not self.auto_move_enabled
        if self.auto_move_enabled:
            self.move_timer.start(AUTO_MOVE_INTERVAL)
        else:
            self.move_timer.stop()
        print(f"🚶 自动移动: {'启用' if self.auto_move_enabled else '禁用'}")
    
    def toggle_smart_mode(self):
        """切换智能模式"""
        self.smart_mode_enabled = not self.smart_mode_enabled
        if self.smart_mode_enabled:
            self.detector.start_monitoring()
        else:
            self.detector.stop_monitoring()
        print(f"🧠 智能模式: {'启用' if self.smart_mode_enabled else '禁用'}")
    
    def resume_smart_mode(self):
        """恢复智能模式"""
        self.manual_override = False
        print("🧠 智能模式已恢复")

    def random_walk(self):
        """随机移动 - 优化版本"""
        if self.is_walking:
            return

        screen = QApplication.primaryScreen().geometry()
        max_x = screen.width() - self.width()
        max_y = screen.height() - self.height()

        import random
        new_x = random.randint(0, max_x)
        new_y = random.randint(0, max_y)

        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(new_x, new_y, self.width(), self.height()))
        self.is_walking = True
        self.animation.start()

    def mousePressEvent(self, event):
        """鼠标按下"""
        if event.button() == Qt.LeftButton:
            self.drag_position = event.globalPosition().toPoint() - self.frameGeometry().topLeft()
        elif event.button() == Qt.RightButton:
            self.show_context_menu(event.globalPosition().toPoint())

    def mouseMoveEvent(self, event):
        """鼠标移动"""
        if event.buttons() == Qt.LeftButton and not self.drag_position.isNull():
            self.move(event.globalPosition().toPoint() - self.drag_position)

    def mouseDoubleClickEvent(self, event):
        """双击跳跃"""
        if event.button() == Qt.LeftButton:
            self.jump()

    def jump(self):
        """跳跃动画"""
        if self.is_walking:
            return

        current_pos = self.pos()
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(current_pos.x(), current_pos.y() - JUMP_HEIGHT, self.width(), self.height()))
        self.animation.setDuration(300)
        self.is_walking = True
        self.animation.finished.connect(self.jump_down)
        self.animation.start()

    def jump_down(self):
        """跳跃下落"""
        self.animation.finished.disconnect()
        current_pos = self.pos()
        self.animation.setStartValue(self.geometry())
        self.animation.setEndValue(QRect(current_pos.x(), current_pos.y() + JUMP_HEIGHT, self.width(), self.height()))
        self.animation.setDuration(300)
        self.animation.finished.connect(lambda: setattr(self, 'is_walking', False))
        self.animation.start()

    def show_context_menu(self, position):
        """右键菜单 - 简化版本"""
        menu = QMenu(self)

        # 状态信息
        work_minutes = int(self.detector.get_work_time_minutes())
        music_running = self.detector.audio_detector.cached_result
        status_action = QAction(f"📊 状态: {self.current_gif} | 工作: {work_minutes}分钟 | 音乐: {'运行' if music_running else '未运行'}", self)
        status_action.setEnabled(False)
        menu.addAction(status_action)

        menu.addSeparator()

        # 切换动画
        switch_action = QAction(f"🔄 切换动画", self)
        switch_action.triggered.connect(self.switch_gif)
        menu.addAction(switch_action)

        # 自动移动
        move_text = "禁用自动移动" if self.auto_move_enabled else "启用自动移动"
        move_action = QAction(f"🚶 {move_text}", self)
        move_action.triggered.connect(self.toggle_auto_move)
        menu.addAction(move_action)

        # 智能模式
        smart_text = "禁用智能模式" if self.smart_mode_enabled else "启用智能模式"
        smart_action = QAction(f"🧠 {smart_text}", self)
        smart_action.triggered.connect(self.toggle_smart_mode)
        menu.addAction(smart_action)

        menu.addSeparator()

        # 说话功能
        talk_action = QAction("💬 说句话", self)
        talk_action.triggered.connect(self.say_something)
        menu.addAction(talk_action)

        menu.addSeparator()

        # 隐藏和退出
        hide_action = QAction("🙈 隐藏", self)
        hide_action.triggered.connect(self.hide)
        menu.addAction(hide_action)

        quit_action = QAction("❌ 退出", self)
        quit_action.triggered.connect(QApplication.quit)
        menu.addAction(quit_action)

        menu.exec(position)

    def say_something(self):
        """主动说话"""
        import random
        if self.current_gif in self.messages:
            message = random.choice(self.messages[self.current_gif])
            self.bubble.show_message(message)

    def closeEvent(self, event):
        """关闭事件"""
        self.detector.stop_monitoring()
        event.accept()


def main():
    """主函数"""
    app = QApplication(sys.argv)

    try:
        # 检查GIF文件
        required_files = ["./frames/music.gif", "./frames/work.gif", "./frames/no.gif"]
        missing_files = [f for f in required_files if not os.path.exists(f)]

        if missing_files:
            print(f"❌ 缺少GIF文件: {missing_files}")
            return

        # 创建桌面宠物
        pet = DesktopPet()
        pet.show()

        # 设置初始位置
        screen = app.primaryScreen().geometry()
        pet.move(screen.width() // 2, screen.height() // 2)

        print("🎉 桌面宠物V5.0启动成功！")
        print("🎵 极简音频检测: 只检测网易云和QQ音乐")
        print("⚡ 大幅性能优化: 减少70%资源占用")
        print("🎮 右键菜单，左键拖拽，双击跳跃")

        sys.exit(app.exec())

    except Exception as e:
        print(f"❌ 启动失败: {e}")


if __name__ == '__main__':
    main()
