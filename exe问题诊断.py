#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
exe文件问题诊断工具
"""

import os
import sys
import subprocess

def check_exe_file():
    """检查exe文件状态"""
    print("🔍 检查exe文件状态...")
    
    exe_paths = [
        "./dist/桌面宠物.exe",
        "./桌面宠物_最终版/桌面宠物.exe"
    ]
    
    for exe_path in exe_paths:
        if os.path.exists(exe_path):
            file_size = os.path.getsize(exe_path) / (1024 * 1024)
            print(f"✅ 找到exe文件: {exe_path}")
            print(f"📊 文件大小: {file_size:.1f} MB")
            
            # 检查文件权限
            if os.access(exe_path, os.R_OK):
                print("✅ 文件可读")
            else:
                print("❌ 文件不可读")
                
            if os.access(exe_path, os.X_OK):
                print("✅ 文件可执行")
            else:
                print("❌ 文件不可执行")
            
            return exe_path
        else:
            print(f"❌ 未找到: {exe_path}")
    
    return None

def check_dependencies():
    """检查依赖文件"""
    print("\n🔍 检查依赖文件...")
    
    required_files = [
        "./frames/music.gif",
        "./frames/work.gif",
        "./frames/no.gif"
    ]
    
    all_exist = True
    for file in required_files:
        if os.path.exists(file):
            print(f"✅ {file}")
        else:
            print(f"❌ 缺少: {file}")
            all_exist = False
    
    return all_exist

def test_python_version():
    """测试Python版本运行"""
    print("\n🔍 测试Python版本运行...")
    
    python_files = [
        "desktop_pet_final.py",
        "desktop_pet.py"
    ]
    
    for py_file in python_files:
        if os.path.exists(py_file):
            print(f"✅ 找到Python文件: {py_file}")
            try:
                # 尝试运行Python版本（5秒后自动结束）
                print(f"🚀 测试运行 {py_file}...")
                result = subprocess.run([
                    sys.executable, py_file
                ], timeout=5, capture_output=True, text=True)
                
                if "启动成功" in result.stdout:
                    print("✅ Python版本可以正常运行")
                    return True
                else:
                    print("⚠️ Python版本运行异常")
                    if result.stderr:
                        print(f"错误信息: {result.stderr}")
                    
            except subprocess.TimeoutExpired:
                print("✅ Python版本启动正常（5秒测试）")
                return True
            except Exception as e:
                print(f"❌ Python版本运行失败: {e}")
    
    return False

def create_debug_exe():
    """创建调试版exe"""
    print("\n🔨 创建调试版exe...")
    
    # 创建带控制台的调试版本
    cmd = [
        "pyinstaller",
        "--onefile",
        "--console",  # 显示控制台，便于调试
        "--name=桌面宠物_调试版",
        "--add-data=frames;frames",
        "--hidden-import=pynput.mouse._win32",
        "--hidden-import=pynput.keyboard._win32", 
        "--hidden-import=psutil",
        "--clean"
    ]
    
    # 选择Python文件
    if os.path.exists("desktop_pet_final.py"):
        cmd.append("desktop_pet_final.py")
        py_file = "desktop_pet_final.py"
    elif os.path.exists("desktop_pet.py"):
        cmd.append("desktop_pet.py")
        py_file = "desktop_pet.py"
    else:
        print("❌ 未找到Python源文件")
        return False
    
    # 添加图标
    if os.path.exists("cat_icon.ico"):
        cmd.insert(-1, "--icon=cat_icon.ico")
    
    try:
        print(f"执行命令: {' '.join(cmd)}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            debug_exe = "./dist/桌面宠物_调试版.exe"
            if os.path.exists(debug_exe):
                print(f"✅ 调试版exe创建成功: {debug_exe}")
                print("💡 请运行调试版查看详细错误信息")
                return True
        else:
            print("❌ 调试版创建失败:")
            print(result.stderr)
            
    except Exception as e:
        print(f"❌ 创建调试版失败: {e}")
    
    return False

def suggest_solutions():
    """建议解决方案"""
    print("\n💡 常见问题解决方案:")
    print()
    
    print("1. 🛡️ 杀毒软件拦截:")
    print("   - 将exe文件添加到杀毒软件白名单")
    print("   - 临时关闭实时保护再运行")
    print("   - 检查Windows Defender是否拦截")
    print()
    
    print("2. 📁 文件路径问题:")
    print("   - 确保exe文件和frames文件夹在同一目录")
    print("   - 避免中文路径或特殊字符")
    print("   - 尝试移动到简单路径如C:\\temp\\")
    print()
    
    print("3. 🔧 系统兼容性:")
    print("   - 确保是Windows 10/11系统")
    print("   - 尝试以管理员身份运行")
    print("   - 检查是否缺少Visual C++运行库")
    print()
    
    print("4. 📦 打包问题:")
    print("   - 运行调试版exe查看错误信息")
    print("   - 检查PyInstaller版本是否最新")
    print("   - 尝试重新打包")
    print()
    
    print("5. 🐍 Python环境:")
    print("   - 先测试Python版本是否正常运行")
    print("   - 检查所有依赖库是否安装")
    print("   - 确认PySide6版本兼容性")

def main():
    """主诊断流程"""
    print("🔧 桌面宠物exe问题诊断工具")
    print("=" * 50)
    
    # 1. 检查exe文件
    exe_path = check_exe_file()
    
    # 2. 检查依赖文件
    deps_ok = check_dependencies()
    
    # 3. 测试Python版本
    python_ok = test_python_version()
    
    # 4. 分析问题
    print("\n📋 诊断结果:")
    if exe_path:
        print("✅ exe文件存在")
    else:
        print("❌ exe文件不存在，需要重新打包")
        
    if deps_ok:
        print("✅ 依赖文件完整")
    else:
        print("❌ 缺少依赖文件")
        
    if python_ok:
        print("✅ Python版本可运行")
    else:
        print("❌ Python版本有问题")
    
    # 5. 提供解决方案
    if exe_path and deps_ok:
        if not python_ok:
            print("\n⚠️ Python版本有问题，建议先修复Python代码")
        else:
            print("\n💡 exe文件和依赖都正常，可能是运行环境问题")
            
        # 创建调试版
        print("\n🔨 是否创建调试版exe以查看详细错误？")
        response = input("输入 y 创建调试版，其他键跳过: ")
        if response.lower() == 'y':
            create_debug_exe()
    
    # 6. 建议解决方案
    suggest_solutions()
    
    print("\n🎯 建议操作顺序:")
    print("1. 先运行Python版本确认功能正常")
    print("2. 检查杀毒软件是否拦截exe")
    print("3. 运行调试版exe查看错误信息")
    print("4. 根据错误信息采取相应措施")

if __name__ == "__main__":
    main()
